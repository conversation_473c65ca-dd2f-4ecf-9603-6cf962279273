package com.ybda.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.model.entity.BaiduGeocodingResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class SearchHttpAK {

    public static String URL = "https://api.map.baidu.com/reverse_geocoding/v3?";

    public static String AK = "cXigmWdvzRlzqn2IQSV9unRu9EX9jlw8";

//    public static void main(String[] args) throws Exception {
//
//        SearchHttpAK snCal = new SearchHttpAK();
//
//        Map params = new LinkedHashMap<String, String>();
//        params.put("ak", AK);   //用户申请注册的key
//        params.put("output", "json");   //输出格式为json或者xml
//        params.put("coordtype", "bd09ll");  //  传入的坐标类型，目前支持的坐标类型包括：bd09ll（百度经纬度坐标）、bd09mc（百度米制坐标）、gcj02ll（国测局经纬度坐标，仅限中国）、wgs84ll（ GPS经纬度）
//        params.put("poi_types", "道路");
//        params.put("extensions_poi", "0");  //extensions_poi=0，不召回pois数据。extensions_poi=1，返回pois数据（默认显示周边1000米内的poi），并返回sematic_description语义化数据。
//        params.put("sort_strategy", "distance");    //配合entire_poi使用，可选择distance距离、rank重要性、default 综合排序三个参数进行对POI结果排序 （排序影响formatted_address_poi的结果
//        params.put("entire_poi", "1");  //设置该参数可召回更多POI，优化 formatted_address_poi的结果， 与sort_strategy参数配合使用效果更佳
//        params.put("location", "28.760116,104.636844");  //根据经纬度坐标获取地址。
//        params.put("radius", "100");    //poi召回半径以下内容需要 extensions_poi=1时才生效；poi召回半径，允许设置区间为0-3000米，超过3000米按3000米召回。
//
//        snCal.requestGetAK(URL, params);
//    }
    public static void requestGet(String latitude, String longitude) throws Exception {
        SearchHttpAK snCal = new SearchHttpAK();
        Map params = new LinkedHashMap<String, String>();
        params.put("ak", AK);   //用户申请注册的key
        params.put("output", "json");   //输出格式为json或者xml
        params.put("coordtype", "wgs84ll");  //  传入的坐标类型，目前支持的坐标类型包括：bd09ll（百度经纬度坐标）、bd09mc（百度米制坐标）、gcj02ll（国测局经纬度坐标，仅限中国）、wgs84ll（ GPS经纬度）
        params.put("location", latitude+","+longitude);  //根据经纬度坐标获取地址。
        params.put("extensions_road", "true");  // 当取值为true时，召回坐标周围最近的3条道路数据。
//        params.put("poi_types", "道路");
//        params.put("extensions_poi", "1");  //extensions_poi=0，不召回pois数据。extensions_poi=1，返回pois数据（默认显示周边1000米内的poi），并返回sematic_description语义化数据。
//        params.put("sort_strategy", "distance");    //配合entire_poi使用，可选择distance距离、rank重要性、default 综合排序三个参数进行对POI结果排序 （排序影响formatted_address_poi的结果
//        params.put("entire_poi", "1");  //设置该参数可召回更多POI，优化 formatted_address_poi的结果， 与sort_strategy参数配合使用效果更佳
//        params.put("radius", "200");    //poi召回半径以下内容需要 extensions_poi=1时才生效；poi召回半径，允许设置区间为0-3000米，超过3000米按3000米召回。
        snCal.requestGetAK(URL, params);
    }

    /**
     * 默认ak
     * 选择了ak，使用IP白名单校验：
     * 根据您选择的AK已为您生成调用代码
     * 检测到您当前的ak设置了IP白名单校验
     * 您的IP白名单中的IP非公网IP，请设置为公网IP，否则将请求失败
     * 请在IP地址为xxxxxxx的计算发起请求，否则将请求失败
     */
    public void requestGetAK(String strUrl, Map<String, String> param) throws Exception {
        if (strUrl == null || strUrl.length() <= 0 || param == null || param.size() <= 0) {
            return;
        }

        StringBuffer queryString = new StringBuffer();
        queryString.append(strUrl);
        for (Map.Entry<?, ?> pair : param.entrySet()) {
            queryString.append(pair.getKey() + "=");
            //    第一种方式使用的 jdk 自带的转码方式  第二种方式使用的 spring 的转码方法 两种均可
            //    queryString.append(URLEncoder.encode((String) pair.getValue(), "UTF-8").replace("+", "%20") + "&");
            queryString.append(UriUtils.encode((String) pair.getValue(), "UTF-8") + "&");
        }

        if (queryString.length() > 0) {
            queryString.deleteCharAt(queryString.length() - 1);
        }

        URL url = new URL(queryString.toString());
//        System.out.println(queryString.toString());
        URLConnection httpConnection = (HttpURLConnection) url.openConnection();
        httpConnection.connect();

        InputStreamReader isr = new InputStreamReader(httpConnection.getInputStream());
        BufferedReader reader = new BufferedReader(isr);
        StringBuffer buffer = new StringBuffer();
        String line;
        while ((line = reader.readLine()) != null) {
            buffer.append(line);
        }
        reader.close();
        isr.close();
        // 使用 Jackson 解析
        ObjectMapper mapper = new ObjectMapper();
        BaiduGeocodingResponse response = mapper.readValue(
                buffer.toString(),
                new TypeReference<BaiduGeocodingResponse>() {}
        );
        log.info("识别到的道路{}",response.getResult().getFormatted_address());
        System.out.println("AK: " + buffer.toString());
    }
}
