package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ybda.model.entity.Device;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备信息Mapper接口
 * 提供设备的数据库操作方法
 */
@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    /**
     * 分页查询设备列表
     * @param page 分页参数
     * @param mobileNo 手机号（可选）
     * @param plateNo 车牌号（可选）
     * @param deviceName 设备名称（可选）
     * @param status 设备状态（可选）
     * @param isOnline 是否在线（可选）
     * @return 设备分页列表
     */
    IPage<Device> selectDevicePage(Page<Device> page,
                                   @Param("mobileNo") String mobileNo,
                                   @Param("plateNo") String plateNo,
                                   @Param("deviceName") String deviceName,
                                   @Param("status") Integer status,
                                   @Param("isOnline") Integer isOnline);

    /**
     * 根据设备ID查询设备信息
     * @param deviceId 设备ID
     * @return 设备信息
     */
    Device selectByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 根据手机号查询设备信息
     * @param mobileNo 手机号
     * @return 设备信息
     */
    Device selectByMobileNo(@Param("mobileNo") String mobileNo);

    /**
     * 更新设备在线状态
     * @param deviceId 设备ID
     * @param isOnline 是否在线
     * @param lastOnlineTime 最后在线时间
     * @return 更新行数
     */
    int updateOnlineStatus(@Param("deviceId") String deviceId,
                          @Param("isOnline") Integer isOnline,
                          @Param("lastOnlineTime") LocalDateTime lastOnlineTime);

    /**
     * 批量更新设备离线状态
     * @param deviceIds 设备ID列表
     * @param lastOnlineTime 最后在线时间
     * @return 更新行数
     */
    int batchUpdateOfflineStatus(@Param("deviceIds") List<String> deviceIds,
                                @Param("lastOnlineTime") LocalDateTime lastOnlineTime);

    /**
     * 查询在线设备数量
     * @return 在线设备数量
     */
    Long countOnlineDevices();

    /**
     * 查询设备总数
     * @return 设备总数
     */
    Long countTotalDevices();

    /**
     * 查询启用状态的设备列表
     * @return 启用的设备列表
     */
    List<Device> selectEnabledDevices();
}
