package com.ybda.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ybda.model.dto.DeviceDTO;
import com.ybda.model.dto.DeviceQueryDTO;
import com.ybda.model.entity.Device;
import com.ybda.model.vo.DeviceVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备管理Service接口
 * 提供设备的业务逻辑处理
 */
public interface DeviceService extends IService<Device> {

    /**
     * 分页查询设备列表
     * @param queryDTO 查询条件
     * @return 设备分页列表
     */
    IPage<DeviceVO> getDevicePage(DeviceQueryDTO queryDTO);

    /**
     * 根据ID查询设备详情
     * @param id 设备ID
     * @return 设备详情
     */
    DeviceVO getDeviceById(Long id);

    /**
     * 根据设备ID查询设备信息
     * @param deviceId 设备ID
     * @return 设备信息
     */
    Device getDeviceByDeviceId(String deviceId);

    /**
     * 根据手机号查询设备信息
     * @param mobileNo 手机号
     * @return 设备信息
     */
    Device getDeviceByMobileNo(String mobileNo);

    /**
     * 添加设备
     * @param deviceDTO 设备信息
     * @return 是否成功
     */
    boolean addDevice(DeviceDTO deviceDTO);

    /**
     * 更新设备
     * @param deviceDTO 设备信息
     * @return 是否成功
     */
    boolean updateDevice(DeviceDTO deviceDTO);

    /**
     * 删除设备
     * @param id 设备ID
     * @return 是否成功
     */
    boolean deleteDevice(Long id);

    /**
     * 批量删除设备
     * @param ids 设备ID列表
     * @return 是否成功
     */
    boolean batchDeleteDevice(List<Long> ids);

    /**
     * 启用/禁用设备
     * @param id 设备ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateDeviceStatus(Long id, Integer status);

    /**
     * 更新设备在线状态
     * @param deviceId 设备ID
     * @param isOnline 是否在线
     * @param lastOnlineTime 最后在线时间
     * @return 是否成功
     */
    boolean updateOnlineStatus(String deviceId, Integer isOnline, LocalDateTime lastOnlineTime);

    /**
     * 批量更新设备离线状态
     * @param deviceIds 设备ID列表
     * @param lastOnlineTime 最后在线时间
     * @return 是否成功
     */
    boolean batchUpdateOfflineStatus(List<String> deviceIds, LocalDateTime lastOnlineTime);

    /**
     * 验证设备是否有效（用于JT808协议认证）
     * @param deviceId 设备ID
     * @param mobileNo 手机号
     * @return 是否有效
     */
    boolean validateDevice(String deviceId, String mobileNo);

    /**
     * 获取设备统计信息
     * @return 统计信息
     */
    DeviceStatisticsVO getDeviceStatistics();

    /**
     * 获取启用状态的设备列表
     * @return 设备列表
     */
    List<Device> getEnabledDevices();

    /**
     * 设备统计信息VO
     */
    class DeviceStatisticsVO {
        /** 设备总数 */
        private Long totalDevices;
        /** 在线设备数 */
        private Long onlineDevices;
        /** 离线设备数 */
        private Long offlineDevices;
        /** 启用设备数 */
        private Long enabledDevices;
        /** 禁用设备数 */
        private Long disabledDevices;

        // Getters and Setters
        public Long getTotalDevices() { return totalDevices; }
        public void setTotalDevices(Long totalDevices) { this.totalDevices = totalDevices; }
        public Long getOnlineDevices() { return onlineDevices; }
        public void setOnlineDevices(Long onlineDevices) { this.onlineDevices = onlineDevices; }
        public Long getOfflineDevices() { return offlineDevices; }
        public void setOfflineDevices(Long offlineDevices) { this.offlineDevices = offlineDevices; }
        public Long getEnabledDevices() { return enabledDevices; }
        public void setEnabledDevices(Long enabledDevices) { this.enabledDevices = enabledDevices; }
        public Long getDisabledDevices() { return disabledDevices; }
        public void setDisabledDevices(Long disabledDevices) { this.disabledDevices = disabledDevices; }
    }
}
