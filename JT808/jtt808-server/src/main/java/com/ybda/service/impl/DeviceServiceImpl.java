package com.ybda.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ybda.mapper.DeviceMapper;
import com.ybda.model.dto.DeviceDTO;
import com.ybda.model.dto.DeviceQueryDTO;
import com.ybda.model.entity.Device;
import com.ybda.model.vo.DeviceVO;
import com.ybda.service.DeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备管理Service实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    private final DeviceMapper deviceMapper;

    @Override
    public IPage<DeviceVO> getDevicePage(DeviceQueryDTO queryDTO) {
        Page<Device> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<Device> devicePage = deviceMapper.selectDevicePage(page,
                queryDTO.getMobileNo(),
                queryDTO.getPlateNo(),
                queryDTO.getDeviceName(),
                queryDTO.getStatus(),
                queryDTO.getIsOnline());

        // 转换为VO
        IPage<DeviceVO> voPage = new Page<>();
        BeanUtils.copyProperties(devicePage, voPage);
        List<DeviceVO> voList = devicePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public DeviceVO getDeviceById(Long id) {
        Device device = this.getById(id);
        return device != null ? convertToVO(device) : null;
    }

    @Override
    public Device  getDeviceByDeviceId(String deviceId) {
        return deviceMapper.selectByDeviceId(deviceId);
    }

    @Override
    public Device getDeviceByMobileNo(String mobileNo) {
        return deviceMapper.selectByMobileNo(mobileNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addDevice(DeviceDTO deviceDTO) {
        // 检查设备ID是否已存在
        Device existDevice = getDeviceByDeviceId(deviceDTO.getDeviceId());
        if (existDevice != null) {
            throw new RuntimeException("设备ID已存在：" + deviceDTO.getDeviceId());
        }

        // 检查手机号是否已存在
        existDevice = getDeviceByMobileNo(deviceDTO.getMobileNo());
        if (existDevice != null) {
            throw new RuntimeException("设备手机号已存在：" + deviceDTO.getMobileNo());
        }

        Device device = new Device();
        BeanUtils.copyProperties(deviceDTO, device);
        device.setIsOnline(0); // 默认离线
        device.setRegisterTime(LocalDateTime.now());
        device.setCreateTime(LocalDateTime.now());
        device.setUpdateTime(LocalDateTime.now());

        return this.save(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDevice(DeviceDTO deviceDTO) {
        Device existDevice = this.getById(deviceDTO.getId());
        if (existDevice == null) {
            throw new RuntimeException("设备不存在");
        }

        // 检查设备ID是否被其他设备使用
        Device deviceByDeviceId = getDeviceByDeviceId(deviceDTO.getDeviceId());
        if (deviceByDeviceId != null && !deviceByDeviceId.getId().equals(deviceDTO.getId())) {
            throw new RuntimeException("设备ID已被其他设备使用：" + deviceDTO.getDeviceId());
        }

        // 检查手机号是否被其他设备使用
        Device deviceByMobileNo = getDeviceByMobileNo(deviceDTO.getMobileNo());
        if (deviceByMobileNo != null && !deviceByMobileNo.getId().equals(deviceDTO.getId())) {
            throw new RuntimeException("设备手机号已被其他设备使用：" + deviceDTO.getMobileNo());
        }

        Device device = new Device();
        BeanUtils.copyProperties(deviceDTO, device);
        device.setUpdateTime(LocalDateTime.now());

        return this.updateById(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDevice(Long id) {
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteDevice(List<Long> ids) {
        return this.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeviceStatus(Long id, Integer status) {
        Device device = new Device();
        device.setId(id);
        device.setStatus(status);
        device.setUpdateTime(LocalDateTime.now());
        return this.updateById(device);
    }

    @Override
    public boolean updateOnlineStatus(String deviceId, Integer isOnline, LocalDateTime lastOnlineTime) {
        return deviceMapper.updateOnlineStatus(deviceId, isOnline, lastOnlineTime) > 0;
    }

    @Override
    public boolean batchUpdateOfflineStatus(List<String> deviceIds, LocalDateTime lastOnlineTime) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return true;
        }
        return deviceMapper.batchUpdateOfflineStatus(deviceIds, lastOnlineTime) > 0;
    }

    @Override
    public boolean validateDevice(String deviceId, String mobileNo) {
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Device::getDeviceId, deviceId)
                .eq(Device::getMobileNo, mobileNo)
                .eq(Device::getStatus, 1); // 只有启用状态的设备才能通过验证

        Device device = this.getOne(wrapper);
        return device != null;
    }

    @Override
    public DeviceStatisticsVO getDeviceStatistics() {
        DeviceStatisticsVO statistics = new DeviceStatisticsVO();

        // 设备总数
        Long totalDevices = deviceMapper.countTotalDevices();
        statistics.setTotalDevices(totalDevices);

        // 在线设备数
        Long onlineDevices = deviceMapper.countOnlineDevices();
        statistics.setOnlineDevices(onlineDevices);

        // 离线设备数
        statistics.setOfflineDevices(totalDevices - onlineDevices);

        // 启用设备数
        LambdaQueryWrapper<Device> enabledWrapper = new LambdaQueryWrapper<>();
        enabledWrapper.eq(Device::getStatus, 1);
        Long enabledDevices = this.count(enabledWrapper);
        statistics.setEnabledDevices(enabledDevices);

        // 禁用设备数
        statistics.setDisabledDevices(totalDevices - enabledDevices);

        return statistics;
    }

    @Override
    public List<Device> getEnabledDevices() {
        return deviceMapper.selectEnabledDevices();
    }

    /**
     * 转换为VO对象
     */
    private DeviceVO convertToVO(Device device) {
        DeviceVO vo = new DeviceVO();
        BeanUtils.copyProperties(device, vo);
        // 设置状态和在线状态描述
        vo.setStatus(device.getStatus());
        vo.setIsOnline(device.getIsOnline());
        return vo;
    }
}
