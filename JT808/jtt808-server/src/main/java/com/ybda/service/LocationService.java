package com.ybda.service;

import com.ybda.model.entity.DeviceLocation;
import com.ybda.protocol.t808.T0200;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 位置信息服务接口
 * 提供设备位置数据的业务逻辑处理
 */
public interface LocationService {

    /**
     * 保存单个位置信息
     * @param t0200 JT808位置信息
     * @param deviceId 设备ID
     * @param mobileNo 手机号
     * @param plateNo 车牌号
     */
    void saveLocation(T0200 t0200, String deviceId, String mobileNo, String plateNo);

    /**
     * 批量保存位置信息
     * @param t0200List JT808位置信息列表
     * @param deviceId 设备ID
     * @param mobileNo 手机号
     * @param plateNo 车牌号
     */
    void batchSaveLocations(List<T0200> t0200List, String deviceId, String mobileNo, String plateNo);

    /**
     * 获取设备最新位置
     * @param deviceId 设备ID
     * @return 最新位置信息
     */
    DeviceLocation getLatestLocation(String deviceId);

    /**
     * 获取设备轨迹（指定时间范围）
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量（0表示不限制）
     * @return 轨迹点列表
     */
    List<DeviceLocation> getDeviceTrack(String deviceId, LocalDateTime startTime, LocalDateTime endTime, int limit);

    /**
     * 获取设备最近轨迹
     * @param deviceId 设备ID
     * @param count 轨迹点数量
     * @return 轨迹点列表
     */
    List<DeviceLocation> getRecentTrack(String deviceId, int count);

    /**
     * 获取区域内的设备位置
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 位置信息列表
     */
    List<DeviceLocation> getLocationsByArea(double minLng, double maxLng, double minLat, double maxLat,
                                           LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取所有在线设备的最新位置
     * @return 设备位置列表
     */
    List<DeviceLocation> getAllOnlineDeviceLocations();

    /**
     * 统计设备位置记录数量
     * @param deviceId 设备ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 记录数量
     */
    long countLocationRecords(String deviceId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 清理历史位置数据
     * @param beforeTime 清理此时间之前的数据
     * @return 清理的记录数
     */
    long cleanHistoryLocations(LocalDateTime beforeTime);

    /**
     * 逆地理编码 - 根据经纬度获取地址信息
     * @param lng 经度
     * @param lat 纬度
     * @return 地址信息
     */
    String reverseGeocode(double lng, double lat);
}
