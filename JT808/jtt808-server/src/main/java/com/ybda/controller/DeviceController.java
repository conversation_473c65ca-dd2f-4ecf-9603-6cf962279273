package com.ybda.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ybda.model.dto.DeviceDTO;
import com.ybda.model.dto.DeviceQueryDTO;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.vo.DeviceVO;
import com.ybda.service.DeviceService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备管理Controller
 * 提供设备的CRUD操作接口
 */
@Slf4j
@RestController
@RequestMapping("/device")
@RequiredArgsConstructor
@Validated
public class DeviceController {

    private final DeviceService deviceService;

    /**
     * 分页查询设备列表
     */
    @GetMapping("/page")
    public SaResult getDevicePage(DeviceQueryDTO queryDTO) {
        try {
            IPage<DeviceVO> page = deviceService.getDevicePage(queryDTO);
            return SaResult.data(page);
        } catch (Exception e) {
            log.error("查询设备列表失败", e);
            return SaResult.error("查询设备列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询设备详情
     */
    @GetMapping("/{id}")
    public SaResult getDeviceById(@PathVariable Long id) {
        try {
            DeviceVO device = deviceService.getDeviceById(id);
            if (device == null) {
                return SaResult.error("设备不存在");
            }
            return SaResult.data(device);
        } catch (Exception e) {
            log.error("查询设备详情失败", e);
            return SaResult.error("查询设备详情失败：" + e.getMessage());
        }
    }

    /**
     * 添加设备
     */
    @PostMapping
    public SaResult addDevice(@Valid @RequestBody DeviceDTO deviceDTO) {
        try {
            boolean success = deviceService.addDevice(deviceDTO);
            if (success) {
                return SaResult.ok("设备添加成功");
            } else {
                return SaResult.error("设备添加失败");
            }
        } catch (Exception e) {
            log.error("添加设备失败", e);
            return SaResult.error("添加设备失败：" + e.getMessage());
        }
    }

    /**
     * 更新设备
     */
    @PutMapping
    public SaResult updateDevice(@Valid @RequestBody DeviceDTO deviceDTO) {
        try {
            if (deviceDTO.getId() == null) {
                return SaResult.error("设备ID不能为空");
            }
            boolean success = deviceService.updateDevice(deviceDTO);
            if (success) {
                return SaResult.ok("设备更新成功");
            } else {
                return SaResult.error("设备更新失败");
            }
        } catch (Exception e) {
            log.error("更新设备失败", e);
            return SaResult.error("更新设备失败：" + e.getMessage());
        }
    }

    /**
     * 删除设备
     */
    @DeleteMapping("/{id}")
    public SaResult deleteDevice(@PathVariable Long id) {
        try {
            boolean success = deviceService.deleteDevice(id);
            if (success) {
                return SaResult.ok("设备删除成功");
            } else {
                return SaResult.error("设备删除失败");
            }
        } catch (Exception e) {
            log.error("删除设备失败", e);
            return SaResult.error("删除设备失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除设备
     */
    @DeleteMapping("/batch")
    public SaResult batchDeleteDevice(@RequestBody List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return SaResult.error("请选择要删除的设备");
            }
            boolean success = deviceService.batchDeleteDevice(ids);
            if (success) {
                return SaResult.ok("设备批量删除成功");
            } else {
                return SaResult.error("设备批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除设备失败", e);
            return SaResult.error("批量删除设备失败：" + e.getMessage());
        }
    }

    /**
     * 启用/禁用设备
     */
    @PutMapping("/{id}/status/{status}")
    public SaResult updateDeviceStatus(@PathVariable Long id, @PathVariable Integer status) {
        try {
            if (status < 0 || status > 2) {
                return SaResult.error("设备状态值无效");
            }
            boolean success = deviceService.updateDeviceStatus(id, status);
            if (success) {
                String statusDesc = status == 1 ? "启用" : (status == 0 ? "禁用" : "维护中");
                return SaResult.ok("设备" + statusDesc + "成功");
            } else {
                return SaResult.error("设备状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新设备状态失败", e);
            return SaResult.error("更新设备状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备统计信息
     */
//    @GetMapping("/statistics")
//    public SaResult getDeviceStatistics() {
//        try {
//            DeviceService.DeviceStatisticsVO statistics = deviceService.getDeviceStatistics();
//            return SaResult.data(statistics);
//        } catch (Exception e) {
//            log.error("获取设备统计信息失败", e);
//            return SaResult.error("获取设备统计信息失败：" + e.getMessage());
//        }
//    }

    /**
     * 获取启用状态的设备列表（用于下拉选择）
     */
//    @GetMapping("/enabled")
//    public SaResult getEnabledDevices() {
//        try {
//            List<DeviceDO> devices = deviceService.getEnabledDevices();
//            return SaResult.data(devices);
//        } catch (Exception e) {
//            log.error("获取启用设备列表失败", e);
//            return SaResult.error("获取启用设备列表失败：" + e.getMessage());
//        }
//    }

    /**
     * 验证设备是否有效（内部接口，用于JT808协议认证）
     */
//    @GetMapping("/validate")
//    public SaResult validateDevice(@RequestParam String deviceId, @RequestParam String mobileNo) {
//        try {
//            boolean isValid = deviceService.validateDevice(deviceId, mobileNo);
//            return SaResult.data(isValid);
//        } catch (Exception e) {
//            log.error("验证设备失败", e);
//            return SaResult.error("验证设备失败：" + e.getMessage());
//        }
//    }
}
