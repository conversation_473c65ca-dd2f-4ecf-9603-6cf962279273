package com.ybda.endpoint;

import com.ybda.protocol.t808.*;
import io.github.yezhihao.netmc.core.annotation.Async;
import io.github.yezhihao.netmc.core.annotation.AsyncBatch;
import io.github.yezhihao.netmc.core.annotation.Endpoint;
import io.github.yezhihao.netmc.core.annotation.Mapping;
import io.github.yezhihao.netmc.session.Session;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JT808;
import com.ybda.model.entity.Device;
import com.ybda.model.enums.SessionKey;
import com.ybda.service.DeviceService;
import com.ybda.service.FileService;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static com.ybda.protocol.commons.JT808.*;

@Slf4j
@Endpoint
@Component
@RequiredArgsConstructor
public class JT808Endpoint {

    private final FileService fileService;
    private final DeviceService deviceService;

    @Mapping(types = 终端通用应答, desc = "终端通用应答")
    public Object T0001(T0001 message, Session session) {
        session.response(message);
        return null;
    }

    @Mapping(types = 终端心跳, desc = "终端心跳")
    public void T0002(JTMessage message, Session session) {
    }

    @Mapping(types = 终端注销, desc = "终端注销")
    public void T0003(JTMessage message, Session session) {
        session.invalidate();
    }

    @Mapping(types = 查询服务器时间, desc = "查询服务器时间")
    public T8004 T0004(JTMessage message, Session session) {
        T8004 result = new T8004().setDateTime(LocalDateTime.now(ZoneOffset.UTC));
        return result;
    }

    @Mapping(types = 终端补传分包请求, desc = "终端补传分包请求")
    public void T8003(T8003 message, Session session) {
    }

    @Mapping(types = 终端注册, desc = "终端注册")
    public T8100 T0100(T0100 message, Session session) {
        // 详细记录设备注册信息
        log.info("准备注册设备 - 详细信息: deviceId={}, mobileNo={}, provinceId={}, cityId={}, makerId={}, deviceModel={}, plateColor={}, plateNo={}, protocolVersion={}, serialNo={}",
                message.getDeviceId(),
                message.getClientId(),
                message.getProvinceId(),
                message.getCityId(),
                message.getMakerId(),
                message.getDeviceModel(),
                message.getPlateColor(),
                message.getPlateNo(),
                message.getProtocolVersion(),
                message.getSerialNo());

        T8100 result = new T8100();
        result.setResponseSerialNo(message.getSerialNo());

        try {
            // 验证设备是否在白名单中
            boolean isValidDevice = deviceService.validateDevice(message.getDeviceId(), message.getClientId());
            if (!isValidDevice) {
                log.warn("未授权的设备尝试注册: deviceId={}, mobileNo={}", message.getDeviceId(), message.getClientId());
                result.setResultCode(T8100.NotFoundVehicle); // 车辆未找到
                return result;
            }

            // 获取数据库中的设备信息
            Device dbDevice = deviceService.getDeviceByDeviceId(message.getDeviceId());
            if (dbDevice == null) {
                log.warn("设备在白名单中但数据库中不存在: deviceId={}", message.getDeviceId());
                result.setResultCode(T8100.NotFoundVehicle);
                return result;
            }

            // 注册成功，更新设备信息
            session.register(message);

            // 创建会话设备对象，包含数据库信息
            Device sessionDevice = new Device();
            sessionDevice.setId(dbDevice.getId());
            sessionDevice.setDeviceId(dbDevice.getDeviceId());
            sessionDevice.setMobileNo(dbDevice.getMobileNo());
            sessionDevice.setPlateNo(dbDevice.getPlateNo());
            sessionDevice.setDeviceName(dbDevice.getDeviceName());
            sessionDevice.setProtocolVersion(message.getProtocolVersion());
            session.setAttribute(SessionKey.Device, sessionDevice);

            // 更新设备在线状态
            deviceService.updateOnlineStatus(message.getDeviceId(), 1, LocalDateTime.now());

            result.setToken(message.getDeviceId() + "," + dbDevice.getPlateNo());
            result.setResultCode(T8100.Success);

            log.info("设备注册成功: deviceId={}, mobileNo={}, plateNo={}",
                    message.getDeviceId(), message.getClientId(), dbDevice.getPlateNo());

        } catch (Exception e) {
            log.error("设备注册处理异常: deviceId={}, mobileNo={}", message.getDeviceId(), message.getClientId(), e);
            result.setResultCode(T8100.NotFoundTerminal); // 终端未注册
        }

        return result;
    }

    @Mapping(types = 终端鉴权, desc = "终端鉴权")
    public T0001 T0102(T0102 message, Session session) {
        T0001 result = new T0001();
        result.setResponseSerialNo(message.getSerialNo());
        result.setResponseMessageId(message.getMessageId());

        try {
            String[] token = message.getToken().split(",");
            if (token.length == 0) {
                log.warn("终端鉴权Token格式错误: token={}", message.getToken());
                result.setResultCode(T0001.Failure);
                return result;
            }

            String deviceId = token[0];

            // 验证设备是否在白名单中
            boolean isValidDevice = deviceService.validateDevice(deviceId, message.getClientId());
            if (!isValidDevice) {
                log.warn("未授权的设备尝试鉴权: deviceId={}, mobileNo={}", deviceId, message.getClientId());
                result.setResultCode(T0001.Failure);
                return result;
            }

            // 获取数据库中的设备信息
            Device dbDevice = deviceService.getDeviceByDeviceId(deviceId);
            if (dbDevice == null) {
                log.warn("设备在白名单中但数据库中不存在: deviceId={}", deviceId);
                result.setResultCode(T0001.Failure);
                return result;
            }

            // 鉴权成功
            session.register(message);

            // 创建会话设备对象
            Device sessionDevice = new Device();
            sessionDevice.setId(dbDevice.getId());
            sessionDevice.setDeviceId(dbDevice.getDeviceId());
            sessionDevice.setMobileNo(dbDevice.getMobileNo());
            sessionDevice.setPlateNo(dbDevice.getPlateNo());
            sessionDevice.setDeviceName(dbDevice.getDeviceName());
            sessionDevice.setProtocolVersion(message.getProtocolVersion());
            session.setAttribute(SessionKey.Device, sessionDevice);

            // 更新设备在线状态
            deviceService.updateOnlineStatus(deviceId, 1, LocalDateTime.now());

            result.setResultCode(T0001.Success);

            log.info("设备鉴权成功: deviceId={}, mobileNo={}, plateNo={}",
                    deviceId, message.getClientId(), dbDevice.getPlateNo());

        } catch (Exception e) {
            log.error("设备鉴权处理异常: token={}, mobileNo={}", message.getToken(), message.getClientId(), e);
            result.setResultCode(T0001.Failure);
        }

        return result;
    }

    @Mapping(types = 查询终端参数应答, desc = "查询终端参数应答")
    public void T0104(T0104 message, Session session) {
        session.response(message);
    }

    @Mapping(types = 查询终端属性应答, desc = "查询终端属性应答")
    public void T0107(T0107 message, Session session) {
        session.response(message);
    }

    @Mapping(types = 终端升级结果通知, desc = "终端升级结果通知")
    public void T0108(T0108 message, Session session) {
    }

    /**
     * 异步批量处理
     * poolSize：参考数据库CPU核心数量
     * maxElements：最大累积4000条记录处理一次
     * maxWait：最大等待时间1秒
     */
    @AsyncBatch(poolSize = 2, maxElements = 4000, maxWait = 1000)
    @Mapping(types = 位置信息汇报, desc = "位置信息汇报")
    public void T0200(List<T0200> list) {
        // 添加您的位置数据处理逻辑
        for (T0200 location : list) {
            // 保存到数据库
            // 触发告警
            // 地理围栏检查等
        }
    }

    @Mapping(types = 定位数据批量上传, desc = "定位数据批量上传")
    public void T0704(T0704 message) {
    }

    @Mapping(types = {位置信息查询应答, 车辆控制应答}, desc = "位置信息查询应答/车辆控制应答")
    public void T0201_0500(T0201_0500 message, Session session) {
        session.response(message);
    }

    @Mapping(types = 事件报告, desc = "事件报告")
    public void T0301(T0301 message, Session session) {
    }

    @Mapping(types = 提问应答, desc = "提问应答")
    public void T0302(T0302 message, Session session) {
    }

    @Mapping(types = 信息点播_取消, desc = "信息点播/取消")
    public void T0303(T0303 message, Session session) {
    }

    @Mapping(types = 查询区域或线路数据应答, desc = "查询区域或线路数据应答")
    public void T0608(T0608 message, Session session) {
        session.response(message);
    }

    @Mapping(types = 行驶记录数据上传, desc = "行驶记录仪数据上传")
    public void T0700(T0700 message, Session session) {
        session.response(message);
    }

    @Mapping(types = 电子运单上报, desc = "电子运单上报")
    public void T0701(JTMessage message, Session session) {
    }

    @Mapping(types = 驾驶员身份信息采集上报, desc = "驾驶员身份信息采集上报")
    public void T0702(T0702 message, Session session) {
        session.response(message);
    }

    @Mapping(types = CAN总线数据上传, desc = "CAN总线数据上传")
    public void T0705(T0705 message, Session session) {
    }

    @Mapping(types = 多媒体事件信息上传, desc = "多媒体事件信息上传")
    public void T0800(T0800 message, Session session) {
    }

    @Async
    @Mapping(types = 多媒体数据上传, desc = "多媒体数据上传")
    public JTMessage T0801(T0801 message, Session session) {
        if (message.getPacket() == null) {
            T0001 result = new T0001();
            result.copyBy(message);
            result.setMessageId(JT808.平台通用应答);
            result.setSerialNo(session.nextSerialNo());

            result.setResponseSerialNo(message.getSerialNo());
            result.setResponseMessageId(message.getMessageId());
            result.setResultCode(T0001.Success);
            return result;
        }
        fileService.saveMediaFile(message);
        T8800 result = new T8800();
        result.setMediaId(message.getId());
        return result;
    }

    @Mapping(types = 存储多媒体数据检索应答, desc = "存储多媒体数据检索应答")
    public void T0802(T0802 message, Session session) {
        session.response(message);
    }

    @Mapping(types = 摄像头立即拍摄命令应答, desc = "摄像头立即拍摄命令应答")
    public void T0805(T0805 message, Session session) {
        session.response(message);
    }

    @Mapping(types = 数据上行透传, desc = "数据上行透传")
    public void T0900(T0900 message, Session session) {
    }

    @Mapping(types = 数据压缩上报, desc = "数据压缩上报")
    public void T0901(T0901 message, Session session) {
    }

    @Mapping(types = 终端RSA公钥, desc = "终端RSA公钥")
    public void T0A00(T0A00_8A00 message, Session session) {
        session.response(message);
    }
}