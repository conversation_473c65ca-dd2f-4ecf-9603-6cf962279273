package com.ybda.endpoint;

import io.github.yezhihao.netmc.session.Session;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.extern.slf4j.Slf4j;
import com.ybda.spring.SSEService;
import com.ybda.util.JsonUtils;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.codec.JTMessageAdapter;
import com.ybda.protocol.codec.JTMessageDecoder;
import com.ybda.protocol.codec.JTMessageEncoder;
import com.ybda.protocol.commons.JT808;
import com.ybda.protocol.commons.MessageId;

import java.util.HashSet;

@Slf4j
public class JTMessagePushAdapter extends JTMessageAdapter {

    private final SSEService sseService;
    private static final HashSet<Integer> ignoreMsgs = new HashSet<>();

    // 心跳计数器：每20次心跳打印一次日志
    private static volatile int heartbeatCounter = 0;
    private static final int HEARTBEAT_LOG_INTERVAL = 20;

    static {
        ignoreMsgs.add(JT808.平台通用应答);
        ignoreMsgs.add(JT808.定位数据批量上传);
    }

    public JTMessagePushAdapter(JTMessageEncoder messageEncoder, JTMessageDecoder messageDecoder, SSEService sseService) {
        super(messageEncoder, messageDecoder);
        this.sseService = sseService;
    }

    @Override
    public void encodeLog(Session session, JTMessage message, ByteBuf output) {
        int messageId = message.getMessageId();
        String data = MessageId.getName(messageId) + JsonUtils.toJson(message) + ",hex:" + ByteBufUtil.hexDump(output, 0, output.writerIndex());
        sseService.send(message.getClientId(), data);
        if ((!ignoreMsgs.contains(messageId)))
            log.info("{}\n>>>>>-{}", session, data);
    }

    @Override
    public void decodeLog(Session session, JTMessage message, ByteBuf input) {
        if (message != null) {
            int messageId = message.getMessageId();
            String data = MessageId.getName(messageId) + JsonUtils.toJson(message) + ",hex:" + ByteBufUtil.hexDump(input, 0, input.writerIndex());
            sseService.send(message.getClientId(), data);

            // 心跳消息特殊处理：每20次打印一次
            if (messageId == JT808.终端心跳) {
                heartbeatCounter++;
                if (heartbeatCounter >= HEARTBEAT_LOG_INTERVAL) {
                    log.info("{}\n<<<<<-{} [心跳计数: {}次]", session, data, heartbeatCounter);
                    heartbeatCounter = 0; // 重置计数器
                }
            } else if (!ignoreMsgs.contains(messageId)) {
                log.info("{}\n<<<<<-{}", session, data);
            }

            if (!message.isVerified())
                log.error("<<<<<校验码错误session={},payload={}", session, data);
        }
    }

    public static void clearMessage() {
        synchronized (ignoreMsgs) {
            ignoreMsgs.clear();
        }
    }

    public static void addMessage(int messageId) {
        if (!ignoreMsgs.contains(messageId)) {
            synchronized (ignoreMsgs) {
                ignoreMsgs.add(messageId);
            }
        }
    }

    public static void removeMessage(int messageId) {
        if (ignoreMsgs.contains(messageId)) {
            synchronized (ignoreMsgs) {
                ignoreMsgs.remove(messageId);
            }
        }
    }
}