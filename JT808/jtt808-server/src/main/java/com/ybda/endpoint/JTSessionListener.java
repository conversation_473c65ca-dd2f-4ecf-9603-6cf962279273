package com.ybda.endpoint;

import io.github.yezhihao.netmc.core.model.Message;
import io.github.yezhihao.netmc.session.Session;
import io.github.yezhihao.netmc.session.SessionListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.model.entity.Device;
import com.ybda.model.enums.SessionKey;
import com.ybda.service.DeviceService;

import java.time.LocalDateTime;
import java.util.function.BiConsumer;

/**
 * JT808会话监听器
 * 处理设备连接、注册、离线等事件
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JTSessionListener implements SessionListener {

    private final DeviceService deviceService;

    /**
     * 下行消息拦截器
     */
    private static final BiConsumer<Session, Message> requestInterceptor = (session, message) -> {
        JTMessage request = (JTMessage) message;
        request.setClientId(session.getClientId());
        request.setSerialNo(session.nextSerialNo());

        if (request.getMessageId() == 0) {
            request.setMessageId(request.reflectMessageId());
        }

        Device device = session.getAttribute(SessionKey.Device);
        if (device != null) {
            int protocolVersion = device.getProtocolVersion();
            if (protocolVersion > 0) {
                request.setVersion(true);
                request.setProtocolVersion(protocolVersion);
            }
        }
    };

    /**
     * 设备连接
     */
    @Override
    public void sessionCreated(Session session) {
        session.requestInterceptor(requestInterceptor);
        log.debug("设备连接: {}", session.getClientId());
    }

    /**
     * 设备注册
     */
    @Override
    public void sessionRegistered(Session session) {
        Device device = session.getAttribute(SessionKey.Device);
        if (device != null) {
            log.info("设备注册成功: deviceId={}, mobileNo={}", device.getDeviceId(), device.getMobileNo());
        }
    }

    /**
     * 设备离线
     */
    @Override
    public void sessionDestroyed(Session session) {
        try {
            Device device = session.getAttribute(SessionKey.Device);
            if (device != null && device.getDeviceId() != null) {
                // 更新设备离线状态
                deviceService.updateOnlineStatus(device.getDeviceId(), 0, LocalDateTime.now());
                log.info("设备离线: deviceId={}, mobileNo={}", device.getDeviceId(), device.getMobileNo());
            } else {
                log.debug("未注册设备断开连接: {}", session.getClientId());
            }
        } catch (Exception e) {
            log.error("处理设备离线事件异常: {}", session.getClientId(), e);
        }
    }
}