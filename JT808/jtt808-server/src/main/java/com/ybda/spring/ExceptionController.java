package com.ybda.spring;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.exception.SaTokenException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestNotUsableException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import com.ybda.model.responsive.APICodes;
import com.ybda.model.responsive.APIException;
import com.ybda.model.responsive.R;

import java.util.List;

@Slf4j
@RestControllerAdvice
public class ExceptionController {

    @ExceptionHandler(AsyncRequestNotUsableException.class)
    public void onAsyncRequestNotUsableException(AsyncRequestNotUsableException e) {
        log.warn("异步请求已断开：{}", e.getMessage());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public R<?> onIllegalArgumentException(IllegalArgumentException e) {
        log.warn("系统异常:", e);
        return R.error(APICodes.InvalidParameter, e.getMessage());
    }

    @ExceptionHandler(APIException.class)
    public APIException onAPIException(APIException e) {
        return e;
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R<?> onHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.warn("系统异常:", e);
        return R.error(APICodes.TypeMismatch, e);
    }

    @ExceptionHandler(BindException.class)
    public R<?> onBindException(BindException e) {
        List<FieldError> fieldErrors = e.getFieldErrors();
        StringBuilder sb = new StringBuilder();
        for (FieldError fieldError : fieldErrors)
            sb.append(fieldError.getField()).append(fieldError.getDefaultMessage());
        return R.error(APICodes.MissingParameter, sb.toString());
    }

    @ExceptionHandler(HttpMediaTypeNotAcceptableException.class)
    public R<?> onHttpMediaTypeNotAcceptableException(HttpMediaTypeNotAcceptableException e) {
        return R.error(APICodes.NotAcceptable);
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public R<?> onHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e) {
        return R.error(APICodes.UnsupportedMediaType);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R<?> onHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        return R.error(APICodes.NotImplemented);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<?> onMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        FieldError fieldError = e.getBindingResult().getFieldError();
        if (fieldError != null)
            return R.error(fieldError.getDefaultMessage());
        return R.error(APICodes.TypeMismatch, e.getBindingResult().toString());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R<?> onMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        return R.error(APICodes.MissingParameter, ":" + e.getParameterName());
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R<?> onMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        return R.error(APICodes.TypeMismatch, ":" + e.getName() + "=" + e.getValue(), e);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public R<?> onNoHandlerFoundException(NoHandlerFoundException e) {
        return R.error(APICodes.NotFound);
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public R<?> onNoResourceFoundException(NoResourceFoundException e) {
        return R.error(APICodes.NotFound);
    }

    // ========== SaToken异常处理 ==========

    /**
     * 处理未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public R<?> onNotLoginException(NotLoginException e) {
        String type = e.getType();
        String message;
        
        switch (type) {
            case NotLoginException.NOT_TOKEN:
                message = "未提供Token";
                break;
            case NotLoginException.INVALID_TOKEN:
                message = "无效Token";
                break;
            case NotLoginException.TOKEN_TIMEOUT:
                message = "Token已过期";
                break;
            case NotLoginException.BE_REPLACED:
                message = "Token已被顶下线";
                break;
            case NotLoginException.KICK_OUT:
                message = "Token已被踢下线";
                break;
            default:
                message = "当前会话未登录";
                break;
        }
        
        log.warn("JT808登录异常: {}", message);
        return R.error(APICodes.Unauthorized, message);
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R<?> onNotPermissionException(NotPermissionException e) {
        String message = "无访问权限: " + e.getPermission();
        log.warn("JT808权限异常: {}", message);
        return R.error(APICodes.NonPermission, message);
    }

    /**
     * 处理角色不足异常
     */
    @ExceptionHandler(NotRoleException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R<?> onNotRoleException(NotRoleException e) {
        String message = "无角色权限: " + e.getRole();
        log.warn("JT808角色异常: {}", message);
        return R.error(APICodes.NonPermission, message);
    }

    /**
     * 处理其他SaToken异常
     */
    @ExceptionHandler(SaTokenException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R<?> onSaTokenException(SaTokenException e) {
        String message = "权限验证失败: " + e.getMessage();
        log.warn("JT808 SaToken异常: {}", message);
        return R.error(APICodes.NonPermission, message);
    }
}