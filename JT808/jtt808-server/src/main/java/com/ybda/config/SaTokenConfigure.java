package com.ybda.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * JT808服务 Sa-Token 配置类 - 启用注解权限检查
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {
    
    /**
     * 注册 Sa-Token 拦截器，启用注解权限检查
     * 让 @SaCheckPermission 等注解生效
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，拦截所有路径进行注解权限检查
        registry.addInterceptor(new SaInterceptor())
                .addPathPatterns("/**");
                // JT808服务通常不需要排除登录接口，因为设备协议服务有自己的认证机制
                // 如果后续有需要排除的接口，可以添加 .excludePathPatterns("/path")
    }
} 