package com.ybda.config;

import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.util.SaResult;
import com.ybda.service.PermissionCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;

/**
 * JT808服务权限接口实现 - 通过HTTP调用auth-service获取用户权限
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JT808StpInterfaceImpl implements StpInterface {

    private final RestTemplate restTemplate;
    private final PermissionCacheService permissionCacheService;

    @Value("${auth-service.url:http://localhost:8005/auth}")
    private String authServiceUrl;
    
    /**
     * 返回一个账号所拥有的权限码集合
     * 优先从Redis缓存获取，缓存未命中时调用微服务接口并缓存结果
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        String userId = loginId.toString();
        // 先从缓存获取
        List<String> permissions = permissionCacheService.getUserPermissionsFromCache(userId);
        if (permissions != null) {
            log.debug("JT808从缓存获取用户权限: userId={}, permissions={}", userId, permissions);
            return permissions;
        }

        // 缓存未命中，调用微服务接口
        try {
            String url = authServiceUrl + "/internal/user/" + loginId + "/permissions";
            log.debug("JT808缓存未命中，调用权限接口: {}", url);

            ResponseEntity<SaResult> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<SaResult>() {}
            );

            if (response.getBody() != null && response.getBody().getCode() == 200) {
                Object data = response.getBody().getData();
                if (data instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> result = (List<String>) data;

                    // 缓存查询结果
//                    permissionCacheService.cacheUserPermissions(userId, result);

                    log.debug("JT808从微服务获取并缓存用户权限: userId={}, permissions={}", userId, result);
                    return result;
                }
            }

            log.warn("JT808获取用户权限失败: userId={}, response={}", loginId, response.getBody());
            return Collections.emptyList();

        } catch (Exception e) {
            log.error("JT808调用权限接口异常: userId={}", loginId, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     * 优先从Redis缓存获取，缓存未命中时调用微服务接口并缓存结果
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        String userId = loginId.toString();

        // 先从缓存获取
        List<String> roles = permissionCacheService.getUserRolesFromCache(userId);
        if (roles != null) {
            log.debug("JT808从缓存获取用户角色: userId={}, roles={}", userId, roles);
            return roles;
        }

        // 缓存未命中，调用微服务接口
        try {
            String url = authServiceUrl + "/internal/user/" + loginId + "/roles";
            log.debug("JT808缓存未命中，调用角色接口: {}", url);

            ResponseEntity<SaResult> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<SaResult>() {}
            );

            if (response.getBody() != null && response.getBody().getCode() == 200) {
                Object data = response.getBody().getData();
                if (data instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> result = (List<String>) data;

                    // 缓存查询结果
//                    permissionCacheService.cacheUserRoles(userId, result);

                    log.debug("JT808从微服务获取并缓存用户角色: userId={}, roles={}", userId, result);
                    return result;
                }
            }

            log.warn("JT808获取用户角色失败: userId={}, response={}", loginId, response.getBody());
            return Collections.emptyList();

        } catch (Exception e) {
            log.error("JT808调用角色接口异常: userId={}", loginId, e);
            return Collections.emptyList();
        }
    }
} 