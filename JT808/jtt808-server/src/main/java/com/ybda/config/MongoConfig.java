package com.ybda.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Date;

/**
 * MongoDB配置类
 * 处理时间转换，确保正确的时区存储
 */
@Configuration
public class MongoConfig {

    /**
     * 自定义MongoDB转换器
     * 确保LocalDateTime正确转换为UTC时间存储
     */
    @Bean
    public MongoCustomConversions customConversions() {
        return new MongoCustomConversions(Arrays.asList(
            new LocalDateTimeToDateConverter(),
            new DateToLocalDateTimeConverter()
        ));
    }

    /**
     * LocalDateTime转Date转换器
     * 将北京时间的LocalDateTime转换为UTC的Date存储到MongoDB
     */
    public static class LocalDateTimeToDateConverter implements Converter<LocalDateTime, Date> {
        @Override
        public Date convert(LocalDateTime source) {
            if (source == null) {
                return null;
            }
            // 将LocalDateTime视为北京时间，转换为UTC时间存储
            return Date.from(source.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
        }
    }

    /**
     * Date转LocalDateTime转换器
     * 将MongoDB中的UTC时间转换为北京时间的LocalDateTime
     */
    public static class DateToLocalDateTimeConverter implements Converter<Date, LocalDateTime> {
        @Override
        public LocalDateTime convert(Date source) {
            if (source == null) {
                return null;
            }
            // 将UTC时间转换为北京时间
            return source.toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        }
    }
}
