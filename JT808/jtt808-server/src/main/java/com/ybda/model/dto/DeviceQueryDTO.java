package com.ybda.model.dto;

import lombok.Data;

/**
 * 设备查询条件DTO
 * 用于设备列表查询的参数封装
 */
@Data
public class DeviceQueryDTO {

    /** 当前页码 */
    private Long current = 1L;

    /** 每页大小 */
    private Long size = 10L;

//    /** 设备ID */
//    private String deviceId;

    /** 设备手机号 */
    private String mobileNo;

    /** 车牌号 */
    private String plateNo;

    /** 设备名称 */
    private String deviceName;

//    /** 设备类型 */
//    private String deviceType;

//    /** 设备厂商 */
//    private String manufacturer;

    /** 设备状态 - 0:禁用 1:启用 2:维护中 */
    private Integer status;

    /** 是否在线 - 0:离线 1:在线 */
    private Integer isOnline;
}
