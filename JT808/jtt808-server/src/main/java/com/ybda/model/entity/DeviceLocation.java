package com.ybda.model.entity;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 设备位置信息实体类 - MongoDB存储
 * 对应JT808协议T0200位置信息汇报
 */
@Data
@Accessors(chain = true)
@Document(collection = "device_location")
@CompoundIndexes({
    @CompoundIndex(name = "device_time_idx", def = "{'deviceId': 1, 'deviceTime': -1}"),
    @CompoundIndex(name = "location_idx", def = "{'longitude': 1, 'latitude': 1}")
})
public class DeviceLocation {

    /** MongoDB文档ID */
    @Id
    private String id;

    /** 设备ID */
    @Field("device_id")
    @Indexed
    private String deviceId;

    /** 设备手机号 */
    @Field("mobile_no")
    private String mobileNo;

    /** 车牌号 */
    @Field("plate_no")
    private String plateNo;

    /** 报警标志位 */
    @Field("warn_bit")
    private Integer warnBit;

    /** 状态标志位 */
    @Field("status_bit")
    private Integer statusBit;

//    /** 纬度（原始值，需除以1000000得到实际纬度） */
//    @Field("latitude")
//    private Integer latitude;
//
//    /** 经度（原始值，需除以1000000得到实际经度） */
//    @Field("longitude")
//    private Integer longitude;

    /** 实际纬度（度） */
    @Field("lat")
    private Double lat;

    /** 实际经度（度） */
    @Field("lng")
    private Double lng;

    /** 高程（米） */
    @Field("altitude")
    private Integer altitude;

//    /** 速度（1/10公里每小时） */
//    @Field("speed")
//    private Integer speed;

    /** 实际速度（公里每小时） */
    @Field("speed_kph")
    private Float speedKph;

    /** 方向（0-359度，正北为0，顺时针） */
    @Field("direction")
    private Integer direction;

    /** 设备时间（GPS时间，协议已处理为正确时区） */
    @Field("device_time")
    @Indexed
    private LocalDateTime deviceTime;

    /** 位置附加信息（处理后的可读格式） */
    @Field("attributes")
    private Map<String, Object> attributes;

    /** 地址信息（逆地理编码结果） */
    @Field("address")
    private String address;

    /** 省份 */
    @Field("province")
    private String province;

    /** 城市 */
    @Field("city")
    private String city;

    /** 区县 */
    @Field("district")
    private String district;

    /** 街道 */
    @Field("street")
    private String street;

    /** 是否有效位置（GPS定位状态） */
    @Field("is_valid")
    private Boolean isValid;

    /** 卫星数量 */
    @Field("satellite_count")
    private Integer satelliteCount;

    /** 数据接收时间（服务器时间） */
    @Field("receive_time")
    @Indexed
    private LocalDateTime receiveTime;

    /** 创建时间 */
    @Field("create_time")
    private LocalDateTime createTime;

    /**
     * 获取实际纬度
     */
    public Double getLat() {
        return latitude != null ? latitude / 1000000.0 : null;
    }

    /**
     * 获取实际经度
     */
    public Double getLng() {
        return longitude != null ? longitude / 1000000.0 : null;
    }

    /**
     * 获取实际速度（公里每小时）
     */
    public Float getSpeedKph() {
        return speed != null ? speed / 10.0f : null;
    }

    /**
     * 获取指定key的附加信息值
     */
    public Object getAttribute(String key) {
        if (attributes != null) {
            return attributes.get(key);
        }
        return null;
    }

    /**
     * 获取指定key的附加信息字符串值
     */
    public String getAttributeString(String key) {
        Object value = getAttribute(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 判断是否为有效GPS定位
     */
    public boolean isGpsValid() {
        return isValid != null && isValid;
    }

    /**
     * 判断是否有报警信息
     */
    public boolean hasAlarm() {
        return warnBit != null && warnBit > 0;
    }
}
