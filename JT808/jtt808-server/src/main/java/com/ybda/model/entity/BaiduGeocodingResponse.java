package com.ybda.model.entity;

import lombok.Data;

import java.util.List;

/**
 * 百度地图逆地理编码API返回结果
 */
@Data
public class BaiduGeocodingResponse {

    /**
     * 状态码（0：成功，其他：失败）
     */
    private int status;

    /**
     * 返回结果数据
     */
    private Result result;


    /**
     * 返回结果详情
     */
    @Data
    public static class Result {
        /**
         * 经纬度坐标
         */
        private Location location;

        /**
         * 结构化地址（不包含POI信息）
         * 示例："四川省成都市青羊区人民中路一段"
         */
        private String formatted_address;

        /**
         * 结构化地址（包含POI信息，需设置extensions_poi=1）
         * 示例："四川省成都市青羊区西御河街道成都市实验小学(人民中路校区)"
         */
        private String formatted_address_poi;

        /**
         * 当经纬度定位到AOI面时，返回AOI内的POI描述
         * 示例："第一郡内,亮晶视光配镜东北258米"（需设置extensions_poi=1）
         */
        private String sematic_description;

        /**
         * 地址组成信息
         */
        private AddressComponent addressComponent;

        /**
         * 百度定义的城市ID（建议使用adcode）
         */
        private int cityCode;

        /**
         * 所属开发区信息
         */
        private Edz edz;

        /**
         * 坐标所在商圈信息（多个商圈用逗号分隔）
         * 示例："人民大学,中关村,苏州街"
         */
        private String business;

        /**
         * 商圈详细信息列表
         */
        private List<BusinessInfo> business_info;

        /**
         * 周边道路信息
         */
        private List<Road> roads;

        /**
         * 周边POI（兴趣点）信息
         */
        private List<Poi> pois;

        /**
         * POI所属区域信息
         */
        private List<PoiRegion> poiRegions;

    }

    /**
     * 经纬度坐标
     */
    @Data
    public static class Location {
        /**
         * 经度
         */
        private float lng;

        /**
         * 纬度
         */
        private float lat;

    }

    /**
     * 地址组成信息（国家、省、市、区等）
     */
    @Data
    public static class AddressComponent {
        /**
         * 国家名称
         */
        private String country;

        /**
         * 国家编码（百度自定义）
         */
        private int country_code;

        /**
         * 国家ISO代码（三位，如CHN）
         */
        private String country_code_iso;

        /**
         * 国家ISO代码（两位，如CN）
         */
        private String country_code_iso2;

        /**
         * 行政区划代码（如：510105）
         */
        private int adcode;

        /**
         * 省份名称
         */
        private String province;

        /**
         * 城市名称（省辖县或县级市可能为空）
         */
        private String city;

        /**
         * 城市级别（国外行政区划参考）
         */
        private int city_level;

        /**
         * 区县名称
         */
        private String district;

        /**
         * 乡镇名称
         */
        private String town;

        /**
         * 乡镇ID
         */
        private String town_code;

        /**
         * 社区/村名称（需商用授权）
         */
        private String village;

        /**
         * 社区/村ID（需商用授权）
         */
        private String village_code;

        /**
         * 道路名称
         */
        private String street;

        /**
         * 门牌号
         */
        private String street_number;

        /**
         * 相对当前坐标的方向（有门牌号时返回）
         */
        private String direction;

        /**
         * 相对当前坐标的距离（有门牌号时返回）
         */
        private String distance;

    }

    /**
     * 开发区/工业区信息
     */
    @Data
    public static class Edz {
        /**
         * 开发区名称
         */
        private String name;

    }

    /**
     * 商圈详细信息
     */
    @Data
    public static class BusinessInfo {
        /**
         * 商圈名称
         */
        private String name;

        /**
         * 商圈中心点坐标
         */
        private Location location;

        /**
         * 到输入坐标的直线距离
         */
        private String distance;

        /**
         * 方位关系（如"北"、"南"、"内"等）
         */
        private String direction;
        /**
         * 行政区划代码
         */
        private int adcode; // 新增字段

    }

    /**
     * 道路信息
     */
    @Data
    public static class Road {
        /**
         * 道路名称
         */
        private String name;

        /**
         * 到输入坐标的大概距离
         */
        private String distance;

        /**
         * 方位关系（如"东"、"西"等）
         */
        private String direction;

        /**
         * 坐标点
         */
        private Location location;

    }

    /**
     * POI（兴趣点）信息
     */
    @Data
    public static class Poi {
        /**
         * 地址信息
         */
        private String addr;

        /**
         * 相对当前坐标的方向
         */
        private String direction;

        /**
         * 离坐标点的距离
         */
        private String distance;

        /**
         * POI名称（如"海底捞"）
         */
        private String name;

        /**
         * POI类型（如"美食;中餐厅"）
         */
        private String tag;

        /**
         * POI坐标（{x, y}）
         */
        private Point point;

        /**
         * 联系电话
         */
        private String tel;

        /**
         * POI唯一标识
         */
        private String uid;

        /**
         * 邮编
         */
        private String zip;

        /**
         * 热度值（1-9，越高越热门）
         */
        private String popularity_level;

        /**
         * 主POI信息（如"海底捞"的主POI可能是"上地华联"）
         */
        private Poi parent_poi;

        /**
         * 数据来源（已废弃）
         */
        private String cp;

        /**
         * POI所属AOI名称（需设置entire_poi=1）
         */
        private String aoi_name;

    }

    /**
     * POI坐标点
     */
    @Data
    public static class Point {
        private float x;
        private float y;

    }

    /**
     * POI所属区域信息
     */
    @Data
    public static class PoiRegion {
        /**
         * 坐标与所属区域面的方位关系
         */
        private String direction_desc;

        /**
         * 区域名称
         */
        private String name;

        /**
         * 区域类型
         */
        private String tag;

        /**
         * 离坐标点的距离
         */
        private String distance;

        /**
         * POI唯一标识
         */
        private String uid;

        /**
         * 所属AOI面积（平方米）
         */
        private String region_area;

    }

}
