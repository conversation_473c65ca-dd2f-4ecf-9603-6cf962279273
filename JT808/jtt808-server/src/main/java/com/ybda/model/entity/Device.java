package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.ybda.protocol.t808.T0200;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 设备信息实体类
 * 用于JT808设备管理和认证
 */
@Data
@Accessors(chain = true)
@TableName("device")
public class Device {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 设备ID - JT808协议中的设备ID */
    @TableField("device_id")
    private String deviceId;

    /** 设备手机号 - SIM卡号 */
    @TableField("mobile_no")
    private String mobileNo;

    /** 车牌号 */
    @TableField("plate_no")
    private String plateNo;

    /** 设备名称/别名 */
    @TableField("device_name")
    private String deviceName;

    /** 设备类型 - 车载终端、手持设备等 */
    @TableField("device_type")
    private String deviceType;

    /** 设备厂商 */
    @TableField("manufacturer")
    private String manufacturer;

    /** 设备型号 */
    @TableField("device_model")
    private String deviceModel;

    /** 协议版本号 */
    @TableField("protocol_version")
    private Integer protocolVersion;

    /** 设备状态 - 0:禁用 1:启用 2:维护中 */
    @TableField("status")
    private Integer status;

    /** 是否在线 - 0:离线 1:在线 */
    @TableField("is_online")
    private Integer isOnline;

    /** 最后在线时间 */
    @TableField("last_online_time")
    private LocalDateTime lastOnlineTime;

    /** 注册时间 */
    @TableField("register_time")
    private LocalDateTime registerTime;

    /** 绑定司机ID */
    @TableField("driver_id")
    private Long driverId;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /** 创建人 */
    @TableField("create_by")
    private String createBy;

    /** 更新人 */
    @TableField("update_by")
    private String updateBy;

    /** 实时位置状态 - 不存储到数据库，仅内存使用 */
    @TableField(exist = false)
    private T0200 location;

    /**
     * 更新设备位置信息
     * @param location 位置信息
     */
    public void updateLocation(T0200 location) {
        if (this.location == null) {
            this.location = location;
        } else if (this.location.getDeviceTime().isBefore(location.getDeviceTime())) {
            this.location = location;
        }
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Device other = (Device) that;
        return Objects.equals(this.deviceId, other.deviceId);
    }

    @Override
    public int hashCode() {
        return ((deviceId == null) ? 0 : deviceId.hashCode());
    }
}