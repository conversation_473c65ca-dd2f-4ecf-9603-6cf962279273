package com.ybda.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备信息响应VO
 * 用于返回给前端的设备信息
 */
@Data
public class DeviceVO {

    /** 主键ID */
    private Long id;

    /** 设备ID */
    private String deviceId;

    /** 设备手机号 */
    private String mobileNo;

    /** 车牌号 */
    private String plateNo;

    /** 设备名称/别名 */
    private String deviceName;

    /** 设备类型 */
    private String deviceType;

    /** 设备厂商 */
    private String manufacturer;

    /** 设备型号 */
    private String deviceModel;

    /** 协议版本号 */
    private Integer protocolVersion;

    /** 设备状态 - 0:禁用 1:启用 2:维护中 */
    private Integer status;

    /** 设备状态描述 */
    private String statusDesc;

    /** 是否在线 - 0:离线 1:在线 */
    private Integer isOnline;

    /** 在线状态描述 */
    private String onlineDesc;

    /** 最后在线时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastOnlineTime;

    /** 注册时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    /** 绑定司机ID */
    private Long driverId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 创建人 */
    private String createBy;

    /** 更新人 */
    private String updateBy;

    /**
     * 设置状态描述
     */
    public void setStatus(Integer status) {
        this.status = status;
        if (status != null) {
            switch (status) {
                case 0:
                    this.statusDesc = "禁用";
                    break;
                case 1:
                    this.statusDesc = "启用";
                    break;
                case 2:
                    this.statusDesc = "维护中";
                    break;
                default:
                    this.statusDesc = "未知";
                    break;
            }
        }
    }

    /**
     * 设置在线状态描述
     */
    public void setIsOnline(Integer isOnline) {
        this.isOnline = isOnline;
        if (isOnline != null) {
            this.onlineDesc = isOnline == 1 ? "在线" : "离线";
        }
    }
}
