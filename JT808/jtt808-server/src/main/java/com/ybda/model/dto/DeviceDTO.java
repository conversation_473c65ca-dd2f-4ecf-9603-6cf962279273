package com.ybda.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 设备信息DTO
 * 用于设备添加和更新操作
 */
@Data
public class DeviceDTO {

    /** 主键ID - 更新时必填 */
    private Long id;

    /** 设备ID - JT808协议中的设备ID */
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    /** 设备手机号 - SIM卡号 */
    @NotBlank(message = "设备手机号不能为空")
    private String mobileNo;

    /** 车牌号 */
    private String plateNo;

    /** 设备名称/别名 */
    private String deviceName;

    /** 设备类型 */
    private String deviceType;

    /** 设备厂商 */
    private String manufacturer;

    /** 设备型号 */
    private String deviceModel;

    /** 协议版本号 */
    private Integer protocolVersion;

    /** 设备状态 - 0:禁用 1:启用 2:维护中 */
    @NotNull(message = "设备状态不能为空")
    private Integer status;

    /** 绑定司机ID */
    private Long driverId;

    /** 创建人 */
    private String createBy;

    /** 更新人 */
    private String updateBy;
}
