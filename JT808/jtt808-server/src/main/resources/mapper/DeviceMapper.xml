<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.DeviceMapper">

    <resultMap id="BaseResultMap" type="com.ybda.model.entity.Device">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="device_id" jdbcType="VARCHAR" property="deviceId"/>
        <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo"/>
        <result column="plate_no" jdbcType="VARCHAR" property="plateNo"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer"/>
        <result column="device_model" jdbcType="VARCHAR" property="deviceModel"/>
        <result column="protocol_version" jdbcType="INTEGER" property="protocolVersion"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="is_online" jdbcType="INTEGER" property="isOnline"/>
        <result column="last_online_time" jdbcType="TIMESTAMP" property="lastOnlineTime"/>
        <result column="register_time" jdbcType="TIMESTAMP" property="registerTime"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>

    <!-- 分页查询设备列表 -->
    <select id="selectDevicePage" resultMap="BaseResultMap">
        SELECT
        *
        FROM device
        <where>
            <if test="mobileNo != null and mobileNo != ''">
                AND mobile_no LIKE CONCAT('%', #{mobileNo}, '%')
            </if>
            <if test="plateNo != null and plateNo != ''">
                AND plate_no LIKE CONCAT('%', #{plateNo}, '%')
            </if>
            <if test="deviceName != null and deviceName != ''">
                AND device_name LIKE CONCAT('%', #{deviceName}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="isOnline != null">
                AND is_online = #{isOnline}
            </if>
        </where>
        ORDER BY last_online_time DESC
    </select>

    <!-- 根据设备ID查询设备信息 -->
    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT
        *
        FROM device
        WHERE device_id = #{deviceId}
    </select>

    <!-- 根据手机号查询设备信息 -->
    <select id="selectByMobileNo" resultMap="BaseResultMap">
        SELECT
        *
        FROM device
        WHERE mobile_no = #{mobileNo}
    </select>

    <!-- 更新设备在线状态 -->
    <update id="updateOnlineStatus">
        UPDATE device
        SET is_online = #{isOnline},
            last_online_time = #{lastOnlineTime},
            update_time = NOW()
        WHERE device_id = #{deviceId}
    </update>

    <!-- 批量更新设备离线状态 -->
    <update id="batchUpdateOfflineStatus">
        UPDATE device
        SET is_online = 0,
            last_online_time = #{lastOnlineTime},
            update_time = NOW()
        WHERE device_id IN
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </update>

    <!-- 查询在线设备数量 -->
    <select id="countOnlineDevices" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM device
        WHERE is_online = 1 AND status = 1
    </select>

    <!-- 查询设备总数 -->
    <select id="countTotalDevices" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM device
    </select>

    <!-- 查询启用状态的设备列表 -->
    <select id="selectEnabledDevices" resultMap="BaseResultMap">
        SELECT
        *
        FROM device
        WHERE status = 1
        ORDER BY last_online_time DESC
    </select>

</mapper>