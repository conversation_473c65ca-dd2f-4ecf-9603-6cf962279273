spring:
  application:
    name: jt808-service # 当前服务名称，在 Nacos 注册中心和配置中心中使用
  profiles:
    active: dev # 当前激活环境，dev=开发，prod=生产
    # active: prod  # 当前激活环境，dev=开发，prod=生产
  config:
    import: nacos:jt808-service-${spring.profiles.active}.yaml?group=Asset_Inspection # 根据环境动态导入 Nacos 配置
  datasource:
    url: *************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  cloud:
    nacos:
      config:
        server-addr: ***************:18848 # Nacos 配置中心地址
        username: nacos # Nacos 配置中心用户名
        password: YBda1234 # Nacos 配置中心密码
      discovery:
        group: Asset_Inspection   # 指定服务注册的分组
        server-addr: ***************:18848 # Nacos 注册中心地址
        username: nacos # Nacos 注册中心用户名
        password: YBda1234 # Nacos 注册中心密码

# 日志配置
logging:
  level:
    com.alibaba.nacos: ERROR
    com.alibaba.nacos.client: ERROR
    com.alibaba.nacos.common: ERROR
    com.alibaba.nacos.plugin: ERROR
    org.springframework: WARN
    org.apache: WARN
    io.netty: WARN
    root: WARN