<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ybda</groupId>
        <artifactId>AssetInspection</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>JT808</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>jtt808-protocol</module>
        <module>jtt808-server</module>
    </modules>

    <properties>
        <resource.delimiter>@</resource.delimiter>
        <maven.test.skip>true</maven.test.skip>
    </properties>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>



    <repositories>
        <repository>
            <id>sonatype</id>
            <url>https://repo1.maven.org/maven2/</url>
        </repository>
        <repository>
            <id>apache</id>
            <url>https://repo.maven.apache.org/maven2/</url>
        </repository>
    </repositories>
</project>