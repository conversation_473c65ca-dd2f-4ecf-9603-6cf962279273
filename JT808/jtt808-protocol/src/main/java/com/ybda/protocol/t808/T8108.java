package com.ybda.protocol.t808;

import io.github.yezhihao.protostar.annotation.Field;
import io.github.yezhihao.protostar.annotation.Message;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JT808;

/**
 * <AUTHOR>
 *
 */
@ToString
@Data
@Accessors(chain = true)
@Message(JT808.下发终端升级包)
public class T8108 extends JTMessage {

    public static final int Terminal = 0;
    public static final int CardReader = 12;
    public static final int Beidou = 52;

    @Field(length = 1, desc = "升级类型")
    private int type;
    @Field(length = 5, desc = "制造商ID,终端制造商编码")
    private String makerId;
    @Field(lengthUnit = 1, desc = "版本号")
    private String version;
    @Field(lengthUnit = 4, desc = "数据包")
    private byte[] packet;

}