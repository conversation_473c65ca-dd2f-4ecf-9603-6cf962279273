package com.ybda.protocol.jsatl12;

import io.github.yezhihao.protostar.annotation.Field;
import io.github.yezhihao.protostar.annotation.Message;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JSATL12;

/**
 * <AUTHOR>
 * 
 */
@ToString
@Data
@Accessors(chain = true)
@Message({JSATL12.文件信息上传, JSATL12.文件上传完成消息})
public class T1211 extends JTMessage {

    @Field(lengthUnit = 1, desc = "文件名称")
    private String name;
    @Field(length = 1, desc = "文件类型 0.图片 1.音频 2.视频 3.文本 4.面部特征图片(粤标) 5.其它")
    private int type;
    @Field(length = 4, desc = "文件大小")
    private long size;

}