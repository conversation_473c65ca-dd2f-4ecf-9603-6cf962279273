package com.ybda.protocol.t808;

import io.github.yezhihao.protostar.annotation.Field;
import io.github.yezhihao.protostar.annotation.Message;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JT808;

/**
 * <AUTHOR>
 *
 */
@ToString
@Data
@Accessors(chain = true)
@Message(JT808.电话回拨)
public class T8400 extends JTMessage {

    /** 通话 */
    public static final int Normal = 0;
    /** 监听 */
    public static final int Listen = 1;

    @Field(length = 1, desc = "类型：0.通话 1.监听")
    private int type;
    @Field(length = 20, desc = "电话号码")
    private String phoneNumber;

}