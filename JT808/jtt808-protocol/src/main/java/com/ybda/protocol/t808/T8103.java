package com.ybda.protocol.t808;

import com.ybda.protocol.commons.transform.parameter.*;
import io.github.yezhihao.netmc.util.AdapterMap;
import io.github.yezhihao.protostar.annotation.Field;
import io.github.yezhihao.protostar.annotation.Message;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JT808;
import com.ybda.protocol.commons.transform.ParameterConverter;

import java.util.Map;
import java.util.TreeMap;
import java.util.function.Function;

/**
 * <AUTHOR>
 * 
 */
@ToString
@Data
@Accessors(chain = true)
@Message(JT808.设置终端参数)
public class T8103 extends JTMessage {

    @Field(totalUnit = 1, desc = "参数项列表", converter = ParameterConverter.class)
    private Map<Integer, Object> parameters;

    public T8103 addParameter(Integer key, Object value) {
        if (parameters == null)
            parameters = new TreeMap<>();
        parameters.put(key, value);
        return this;
    }

    /** 数值型参数列表(BYTE、WORD) */
    private Map<Integer, Integer> parametersInt;
    /** 数值型参数列表(DWORD、QWORD) */
    private Map<Integer, String> parametersLong;
    /** 字符型参数列表 */
    private Map<Integer, String> parametersStr;
    /** 图像分析报警参数设置(1078) */
    private ParamImageIdentifyAlarm paramImageIdentifyAlarm;
    /** 特殊报警录像参数设置(1078) */
    private ParamVideoSpecialAlarm paramVideoSpecialAlarm;
    /** 音视频通道列表设置(1078) */
    private ParamChannels paramChannels;
    /** 终端休眠唤醒模式设置数据格式(1078) */
    private ParamSleepWake paramSleepWake;
    /** 音视频参数设置(1078) */
    private ParamVideo paramVideo;
    /** 单独视频通道参数设置(1078) */
    private ParamVideoSingle paramVideoSingle;
    /** 盲区监测系统参数(苏标) */
    private ParamBSD paramBSD;
    /** 胎压监测系统参数(苏标) */
    private ParamTPMS paramTPMS;
    /** 驾驶员状态监测系统参数(苏标) */
    private ParamDSM paramDSM;
    /** 高级驾驶辅助系统参数(苏标) */
    private ParamADAS paramADAS;

    public T8103 build() {
        Map<Integer, Object> map = new TreeMap<>();

        if (parametersInt != null && !parametersInt.isEmpty())
            map.putAll(parametersInt);

        if (parametersLong != null && !parametersLong.isEmpty())
            map.putAll(new AdapterMap(parametersLong, (Function<String, Long>) Long::parseLong));

        if (parametersStr != null && !parametersStr.isEmpty())
            map.putAll(parametersStr);

        if (paramADAS != null)
            map.put(paramADAS.key, paramADAS);
        if (paramBSD != null)
            map.put(paramBSD.key, paramBSD);
        if (paramChannels != null)
            map.put(paramChannels.key, paramChannels);
        if (paramDSM != null)
            map.put(paramDSM.key, paramDSM);
        if (paramImageIdentifyAlarm != null)
            map.put(paramImageIdentifyAlarm.key, paramImageIdentifyAlarm);
        if (paramSleepWake != null)
            map.put(paramSleepWake.key, paramSleepWake);
        if (paramTPMS != null)
            map.put(paramTPMS.key, paramTPMS);
        if (paramVideo != null)
            map.put(paramVideo.key, paramVideo);
        if (paramVideoSingle != null)
            map.put(paramVideoSingle.key, paramVideoSingle);
        if (paramVideoSpecialAlarm != null)
            map.put(paramVideoSpecialAlarm.key, paramVideoSpecialAlarm);

        this.parameters = map;
        return this;
    }
}