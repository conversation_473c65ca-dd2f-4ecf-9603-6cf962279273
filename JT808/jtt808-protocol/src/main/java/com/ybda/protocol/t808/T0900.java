package com.ybda.protocol.t808;

import io.github.yezhihao.protostar.annotation.Field;
import io.github.yezhihao.protostar.annotation.Message;
import io.github.yezhihao.protostar.util.KeyValuePair;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JT808;
import com.ybda.protocol.commons.transform.PassthroughConverter;

/**
 * <AUTHOR>
 * 
 */
@ToString
@Data
@Accessors(chain = true)
@Message(JT808.数据上行透传)
public class T0900 extends JTMessage {

    /** GNSS模块详细定位数据 */
    public static final int GNSSLocation = 0x00;
    /** 道路运输证IC卡信息上传消息为64Byte,下传消息为24Byte,道路运输证IC卡认证透传超时时间为30s.超时后,不重发 */
    public static final int ICCardInfo = 0x0B;
    /** 串口1透传消息 */
    public static final int SerialPortOne = 0x41;
    /** 串口2透传消息 */
    public static final int SerialPortTow = 0x42;
    /** 用户自定义透传 0xF0~0xFF */
    public static final int Custom = 0xF0;

    @Field(desc = "透传消息", converter = PassthroughConverter.class)
    private KeyValuePair<Integer, Object> message;

}