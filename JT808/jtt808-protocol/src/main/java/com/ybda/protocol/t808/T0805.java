package com.ybda.protocol.t808;

import io.github.yezhihao.netmc.core.model.Response;
import io.github.yezhihao.protostar.annotation.Field;
import io.github.yezhihao.protostar.annotation.Message;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JT808;

/**
 * <AUTHOR>
 * 
 */
@ToString
@Data
@Accessors(chain = true)
@Message(JT808.摄像头立即拍摄命令应答)
public class T0805 extends JTMessage implements Response {

    @Field(length = 2, desc = "应答流水号")
    private int responseSerialNo;
    @Field(length = 1, desc = "结果：0.成功 1.失败 2.通道不支持")
    private int result;
    @Field(totalUnit = 2, desc = "多媒体ID列表")
    private int[] id;

}