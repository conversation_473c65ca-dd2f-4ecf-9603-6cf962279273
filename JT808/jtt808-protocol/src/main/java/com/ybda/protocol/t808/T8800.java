package com.ybda.protocol.t808;

import io.github.yezhihao.protostar.annotation.Field;
import io.github.yezhihao.protostar.annotation.Message;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.ybda.protocol.basics.JTMessage;
import com.ybda.protocol.commons.JT808;

/**
 * <AUTHOR>
 * 
 */
@ToString
@Data
@Accessors(chain = true)
@Message(JT808.多媒体数据上传应答)
public class T8800 extends JTMessage {

    @Field(length = 4, desc = "多媒体ID(大于0) 如收到全部数据包则没有后续字段")
    private int mediaId;
    @Field(totalUnit = 1, desc = "重传包ID列表")
    private short[] id;

}