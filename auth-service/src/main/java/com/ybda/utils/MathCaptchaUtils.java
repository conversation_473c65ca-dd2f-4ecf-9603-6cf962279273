package com.ybda.utils;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class MathCaptchaUtils {

    private final StringRedisTemplate redisTemplate;
    private static final String CAPTCHA_PREFIX = "char_captcha:";
    private static final long CAPTCHA_EXPIRE_MINUTES = 5;

    public MathCaptchaUtils(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 生成字符验证码，返回图片Base64和uuid
     */
    public CaptchaResult generateCaptcha(String uuid) {
        // 生成4位字符验证码，图片宽160高60，干扰线5条
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(160, 60, 4, 5);
        String code = captcha.getCode();
        // 保存到Redis
        redisTemplate.opsForValue().set(CAPTCHA_PREFIX + uuid, code, CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);
        return new CaptchaResult(captcha.getImageBase64());
    }

    /**
     * 校验验证码
     */
    public boolean verify(String uuid, String code) {
        if (uuid == null || code == null) {
            return false;
        }
        String key = CAPTCHA_PREFIX + uuid;
        String savedCode = redisTemplate.opsForValue().get(key);
        if (savedCode != null && savedCode.equalsIgnoreCase(code)) {
            redisTemplate.delete(key);
            return true;
        }
        return false;
    }

    public static class CaptchaResult {
        private final String imageBase64;
        public CaptchaResult(String imageBase64) {
            this.imageBase64 = imageBase64;
        }
        public String getImageBase64() {
            return imageBase64;
        }
    }
} 