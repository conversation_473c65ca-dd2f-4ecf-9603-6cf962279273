package com.ybda.utils;

import lombok.extern.slf4j.Slf4j;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * 数据压缩工具类
 * 提供智能压缩和解压缩功能，优化存储空间
 */
@Slf4j
public class CompressionUtil {
    
    private static final String COMPRESSION_PREFIX = "GZIP:";
    private static final int MIN_COMPRESSION_SIZE = 500; // 500字节以下不压缩
    
    /**
     * 智能压缩：大数据压缩，小数据原样返回
     * @param data 原始数据
     * @return 压缩后的数据或原数据
     */
    public static String smartCompress(String data) {
        if (data == null || data.length() < MIN_COMPRESSION_SIZE) {
            return data;  // 小数据不压缩，避免压缩开销
        }
        
        try {
            String compressed = compress(data);
            // 如果压缩后反而更大，则返回原数据
            if (compressed.length() < data.length()) {
                double ratio = getCompressionRatio(data, compressed);
                log.debug("🗜️ 数据压缩成功: {}字节 → {}字节 (压缩{:.1f}%)", 
                    data.length(), compressed.length(), ratio);
                return compressed;
            } else {
                log.debug("📦 数据太小或压缩效果不佳，保持原样: {}字节", data.length());
                return data;
            }
        } catch (Exception e) {
            log.warn("⚠️ 数据压缩失败，返回原数据: {}", e.getMessage());
            return data;
        }
    }
    
    /**
     * GZIP压缩 + Base64编码
     * @param data 原始字符串
     * @return 压缩编码后的字符串
     * @throws Exception 压缩异常
     */
    public static String compress(String data) throws Exception {
        if (data == null) return null;
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzos = new GZIPOutputStream(baos)) {
            gzos.write(data.getBytes(StandardCharsets.UTF_8));
        }
        
        String compressed = Base64.getEncoder().encodeToString(baos.toByteArray());
        return COMPRESSION_PREFIX + compressed;
    }
    
    /**
     * 智能解压缩：自动检测是否为压缩数据
     * @param data 可能压缩的数据
     * @return 解压缩后的原始数据
     */
    public static String smartDecompress(String data) {
        if (data == null) return null;
        
        if (data.startsWith(COMPRESSION_PREFIX)) {
            try {
                String decompressed = decompress(data);
                log.debug("🔓 数据解压缩成功: {}字节 → {}字节", data.length(), decompressed.length());
                return decompressed;
            } catch (Exception e) {
                log.warn("⚠️ 数据解压缩失败，返回原数据: {}", e.getMessage());
                return data;
            }
        }
        
        return data;  // 非压缩数据直接返回
    }
    
    /**
     * Base64解码 + GZIP解压缩
     * @param compressedData 压缩的数据
     * @return 解压缩后的原始字符串
     * @throws Exception 解压缩异常
     */
    public static String decompress(String compressedData) throws Exception {
        if (compressedData == null || !compressedData.startsWith(COMPRESSION_PREFIX)) {
            return compressedData;
        }
        
        String base64Data = compressedData.substring(COMPRESSION_PREFIX.length());
        byte[] compressed = Base64.getDecoder().decode(base64Data);
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPInputStream gzis = new GZIPInputStream(new ByteArrayInputStream(compressed))) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
        }
        
        return baos.toString(StandardCharsets.UTF_8);
    }
    
    /**
     * 计算压缩比
     * @param original 原始数据
     * @param compressed 压缩后数据
     * @return 压缩比百分比
     */
    public static double getCompressionRatio(String original, String compressed) {
        if (original == null || compressed == null) return 0.0;
        return (1.0 - (double) compressed.length() / original.length()) * 100;
    }
    
    /**
     * 检查数据是否为压缩格式
     * @param data 数据
     * @return 是否为压缩数据
     */
    public static boolean isCompressed(String data) {
        return data != null && data.startsWith(COMPRESSION_PREFIX);
    }
}
