package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.SysMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {
    List<String> getAllPages();
    // 查询常量路由
    List<SysMenu> selectConstantRoutes();

    // 查询用户路由
    List<SysMenu> selectUserRoutes(@Param("userId") Long userId);

    // 查询用户权限
    List<String> selectUserPermissions(@Param("userId") Long userId);
}