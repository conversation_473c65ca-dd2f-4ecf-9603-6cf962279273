package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.SysRolePermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysRolePermissionMapper extends BaseMapper<SysRolePermission> {
    List<Integer> selectPermissionList(@Param("roleId") Integer roleId);

    void insertBatchSomeColumn(@Param("rolePermissions") List<SysRolePermission> rolePermissions);
}