package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    List<String> selectPermissionList(int id);

    List<String> selectroleList(int id);

    Long selectUserPageCount(@Param("userName") String userName,
                           @Param("realName") String realName,
                           @Param("userPhone") String userPhone,
                           @Param("userEmail") String userEmail,
                           @Param("userGender") String userGender,
                           @Param("status") Integer status);


    List<SysUser> listUsersWithoutOrg(@Param("userName") String userName, @Param("realName") String realName, @Param("userEmail") String userEmail);

    List<SysUser> listUsersWithOrgs(@Param("orgIds") List<Integer> orgIds, @Param("userName") String userName, @Param("realName") String realName, @Param("email") String email);

    SysUser selectUser(@Param("id") Integer id);
}