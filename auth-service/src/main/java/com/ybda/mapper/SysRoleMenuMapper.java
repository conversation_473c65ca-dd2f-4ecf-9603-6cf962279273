package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.SysRoleMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {
    List<Integer> selectMenuListByRoleId(@Param("roleId") Integer roleId);

    void insertBatchSomeColumn(@Param("roleMenus") List<SysRoleMenu> roleMenus);
}