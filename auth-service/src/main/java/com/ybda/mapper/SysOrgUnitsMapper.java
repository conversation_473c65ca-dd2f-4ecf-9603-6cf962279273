package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.SysOrgUnits;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysOrgUnitsMapper extends BaseMapper<SysOrgUnits> {
    /**
     * 递归查询所有子组织
     *
     * @param parentIds 父id
     * @return {@link List }<{@link SysOrgUnits }> 子组织列表
     * <AUTHOR>
     * @CreateTime 2024-07-16 - 09:21:02
     */
    List<SysOrgUnits> listAllDescendants(@Param("parentIds") List<Long> parentIds);
}