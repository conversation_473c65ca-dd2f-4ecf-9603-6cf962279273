package com.ybda.controller;


import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.dto.SysMenuRouteDTO;
import com.ybda.model.dto.SysUserRouteDTO;
import com.ybda.service.SysRouteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 路由控制器
 */
@RestController
@RequestMapping("/route")
public class SysRouteController {

    @Autowired
    private SysRouteService sysRouteService;

    /**
     * 获取常量路由
     */
    @GetMapping("/getConstantRoutes")
    public SaResult getConstantRoutes() {
        List<SysMenuRouteDTO> constantRoutes = sysRouteService.getConstantRoutes();
        return SaResult.data(constantRoutes);
    }

    /**
     * 获取用户路由
     */
    @GetMapping("/getUserRoutes")
    public SaResult getUserRoutes() {
        // 获取当前用户ID
        long userId = StpUtil.getLoginIdAsLong();
        SysUserRouteDTO userRoutes = sysRouteService.getUserRoutes(userId);
        // 确保即使没有找到菜单，也返回空数组而不是null
        if (userRoutes.getMenus() == null) {
            userRoutes.setMenus(new ArrayList<>());
        }
        if (userRoutes.getPermissions() == null) {
            userRoutes.setPermissions(new ArrayList<>());
        }
        return SaResult.data(userRoutes);
    }
}
