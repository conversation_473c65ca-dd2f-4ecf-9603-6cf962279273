package com.ybda.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.dto.GatewayLogQueryDTO;
import com.ybda.service.GatewayLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 网关日志管理接口
 */
@Slf4j
@RestController
@RequestMapping("/gateway-log")
@RequiredArgsConstructor
public class GatewayLogController {
    
    private final GatewayLogService gatewayLogService;
    
    /**
     * 分页查询网关日志
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @SaCheckPermission("sys:gateway:log:view")
    @GetMapping("/list")
    public SaResult getGatewayLogList(GatewayLogQueryDTO queryDTO) {
        return gatewayLogService.getGatewayLogList(queryDTO);
    }
    
    /**
     * 根据ID查询网关日志详情
     * @param id 日志ID
     * @return 日志详情
     */
    @SaCheckPermission("sys:gateway:log:view")
    @GetMapping("/{id}")
    public SaResult getGatewayLogById(@PathVariable String id) {
        return gatewayLogService.getGatewayLogById(id);
    }

}
