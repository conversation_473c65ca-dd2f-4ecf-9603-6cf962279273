package com.ybda.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.entity.SysRole;
import com.ybda.service.PermissionCacheService;
import com.ybda.service.SysRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 角色接口
 */
@RestController
@RequestMapping("/role")
@RequiredArgsConstructor
public class SysRoleController {
    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private PermissionCacheService permissionCacheService;

    /**
     * 获取所有角色接口
     *
     * @return 返回所有角色列表
     */
    @SaCheckPermission("sys:role:view")
    @GetMapping("/getAllRoles")
    public SaResult getUserListByPage() {
        return SaResult.data(sysRoleService.list());
    }


    /**
     * 获取角色列表接口
     * @return
     */
    @SaCheckPermission("sys:role:view")
    @GetMapping("/getRoleList")
    public SaResult getRoleList(@RequestParam(required = false) String roleName,
                                @RequestParam(required = false) String roleCode,
                                @RequestParam(required = false) Integer status,
                                @RequestParam(defaultValue = "1") Integer current,
                                @RequestParam(defaultValue = "10") Integer size) {
        return sysRoleService.list(roleName, roleCode, status, current, size);
    }

    /**
     * 添加角色
     */
    @SaCheckPermission("sys:role:add")
    @PostMapping("/addRole")
    public SaResult addRole(@RequestBody SysRole role) {
        return sysRoleService.save(role) ? SaResult.ok("添加成功") : SaResult.error("添加失败");
    }

    /**
     * 修改角色
     */
    @SaCheckPermission("sys:role:update")
    @PostMapping("/updateRole")
    public SaResult updateRole(@RequestBody SysRole role) {
        boolean result = sysRoleService.updateById(role);
        if (result) {
            // 角色修改成功，清除所有用户权限缓存
            permissionCacheService.clearAllUserPermissionsCache();
        }
        return result ? SaResult.ok("修改成功") : SaResult.error("修改失败");
    }

    /**
     * 删除角色
     */
    @SaCheckPermission("sys:role:delete")
    @DeleteMapping("/deleteRole")
    public SaResult deleteRole(Integer id) {
        boolean result = sysRoleService.removeById(id);
        if (result) {
            // 角色删除成功，清除所有用户权限缓存
            permissionCacheService.clearAllUserPermissionsCache();
        }
        return result ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

    /**
     * 批量删除角色
     */
    @SaCheckPermission("sys:role:delete")
    @DeleteMapping("/deleteRoleBatch")
    public SaResult deleteRoleBatch(@RequestBody List<Integer> ids) {
        boolean result = sysRoleService.removeBatchByIds(ids);
        if (result) {
            // 角色批量删除成功，清除所有用户权限缓存
            permissionCacheService.clearAllUserPermissionsCache();
        }
        return result ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

}
