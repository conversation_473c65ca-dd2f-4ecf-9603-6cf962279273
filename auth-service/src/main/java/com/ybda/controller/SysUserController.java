package com.ybda.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ybda.model.dto.SysUserAddDTO;
import com.ybda.model.dto.SysUserUpdateDTO;
import com.ybda.model.entity.SysUser;
import com.ybda.model.entity.SysUserRole;
import com.ybda.service.PermissionCacheService;
import com.ybda.service.SysUserRoleService;
import com.ybda.service.SysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
/**
 * 用户接口
 */
@RestController
@RequestMapping("/systemManage")
@RequiredArgsConstructor
public class SysUserController {
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private PermissionCacheService permissionCacheService;
    /**
     *获取用户管理列表
     */
    @SaCheckPermission("sys:user:view")
    @GetMapping("/getUserManageList")
    public SaResult getUserManageList(@RequestParam(required = false) String userName,
                                      @RequestParam(required = false) String realName,
                                      @RequestParam(required = false) String userEmail,
                                      @RequestParam(required = false) String orgIds,
                                      @RequestParam(defaultValue = "1") Integer current,
                                      @RequestParam(defaultValue = "10") Integer size) {
        List<Integer> orgIdList = Arrays.stream(orgIds.split(",")).map(Integer::parseInt).toList();
        return sysUserService.getUserManageList(userName, realName, userEmail, orgIdList, current, size);
    }
    /**
     * 获取用户详细信息
     */
    @SaCheckPermission("sys:user:view")
    @GetMapping("/getUserById")
    public SaResult getUserById(Integer id) {
        return sysUserService.getUserById(id);
    }

    /**
     * 添加用户接口
     * @return 返回添加用户的结果
     */
    @SaCheckPermission("sys:user:add")
    @PostMapping("/addUser")
    public SaResult addUser(@RequestBody SysUserAddDTO sysUserAddDTO) {
        return sysUserService.addUser(sysUserAddDTO);
    }

    /**
     * 更新用户接口
     * @return 返回更新用户的结果
     */
    @SaCheckPermission("sys:user:update")
    @PostMapping("/updateUser")
    public SaResult updateUser(@RequestBody SysUserUpdateDTO sysUserUpdateDTO) {
        return sysUserService.updateUser(sysUserUpdateDTO);
    }

    /**
     * 删除用户接口
     *
     * @param id 要删除的用户ID，不能为空
     * @return 返回删除用户的结果
     */
    @SaCheckPermission("sys:user:delete")
    @DeleteMapping("/deleteUser")
    public SaResult deleteUser(@RequestParam Integer id) {
        sysUserRoleService.remove(new QueryWrapper<SysUserRole>().eq("user_id", id));
        boolean result = sysUserService.removeById(id);
        if (result) {
            // 用户删除成功，清除该用户的权限缓存
            permissionCacheService.clearUserCache(id);
        }
        return SaResult.data(result);
    }

    /**
     * 批量删除用户接口
     *
     * @param ids 要删除的用户ID数组，不能为空
     * @return 返回批量删除用户的结果
     */
    @SaCheckPermission("sys:user:delete")
    @PostMapping("/batchDeleteUser")
    public SaResult batchDeleteUser(@RequestBody List<Integer> ids) {
        sysUserRoleService.remove(new QueryWrapper<SysUserRole>().in("user_id", ids));
        boolean result = sysUserService.removeBatchByIds(ids);
        if (result) {
            // 用户批量删除成功，清除这些用户的权限缓存
            permissionCacheService.clearUsersCache(ids);
        }
        return SaResult.data(result);
    }

    /**
     * 重置密码
     */
    @SaCheckPermission("sys:user:resetPassword")
    @PostMapping("/resetUserPassword")
    public SaResult resetUserPassword(@RequestParam Long id) {
        SysUser user = sysUserService.selectByPrimaryKey(id);
        user.setPassword("123456");
        return sysUserService.updateById(user) ? SaResult.ok("重置密码成功") : SaResult.error("重置密码失败");
    }

}
