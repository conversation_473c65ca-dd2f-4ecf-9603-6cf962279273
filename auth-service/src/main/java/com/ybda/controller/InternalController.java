package com.ybda.controller;

import cn.dev33.satoken.util.SaResult;
import com.ybda.mapper.SysUserMapper;
import com.ybda.service.PermissionCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 内部接口控制器 - 用于微服务间调用，无需鉴权
 * 提供权限查询服务给其他微服务（如JT808服务）
 */
@Slf4j
@RestController
@RequestMapping("/internal")
@RequiredArgsConstructor
public class InternalController {
    
    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private PermissionCacheService permissionCacheService;
    
    /**
     * 获取用户权限列表（内部接口，无需鉴权）
     * 优先从Redis缓存获取，缓存未命中时查询数据库并缓存结果
     * @param userId 用户ID
     * @return 权限码列表
     */
    @GetMapping(value = "/user/{userId}/permissions", produces = MediaType.APPLICATION_JSON_VALUE)
    public SaResult getUserPermissions(@PathVariable Integer userId) {
        try {
            // 先从缓存获取
            List<String> permissions = permissionCacheService.getUserPermissionsFromCache(userId);
            if (permissions != null) {
                log.debug("内部接口：从缓存获取用户权限 userId={}, permissions={}", userId, permissions);
                return SaResult.data(permissions);
            }

            // 缓存未命中，查询数据库
            log.debug("内部接口：缓存未命中，从数据库查询用户权限 userId={}", userId);
            permissions = sysUserMapper.selectPermissionList(userId);

            // 缓存查询结果
            permissionCacheService.cacheUserPermissions(userId, permissions);

            log.debug("内部接口：从数据库获取并缓存用户权限 userId={}, permissions={}", userId, permissions);
            return SaResult.data(permissions);
        } catch (Exception e) {
            log.error("内部接口：获取用户权限失败 userId={}", userId, e);
            return SaResult.error("获取用户权限失败");
        }
    }
    
    /**
     * 获取用户角色列表（内部接口，无需鉴权）
     * 优先从Redis缓存获取，缓存未命中时查询数据库并缓存结果
     * @param userId 用户ID
     * @return 角色码列表
     */
    @GetMapping(value = "/user/{userId}/roles", produces = MediaType.APPLICATION_JSON_VALUE)
    public SaResult getUserRoles(@PathVariable Integer userId) {
        try {
            // 先从缓存获取
            List<String> roles = permissionCacheService.getUserRolesFromCache(userId);
            if (roles != null) {
                log.debug("内部接口：从缓存获取用户角色 userId={}, roles={}", userId, roles);
                return SaResult.data(roles);
            }

            // 缓存未命中，查询数据库
            log.debug("内部接口：缓存未命中，从数据库查询用户角色 userId={}", userId);
            roles = sysUserMapper.selectroleList(userId);

            // 缓存查询结果
            permissionCacheService.cacheUserRoles(userId, roles);

            log.debug("内部接口：从数据库获取并缓存用户角色 userId={}, roles={}", userId, roles);
            return SaResult.data(roles);
        } catch (Exception e) {
            log.error("内部接口：获取用户角色失败 userId={}", userId, e);
            return SaResult.error("获取用户角色失败");
        }
    }
} 