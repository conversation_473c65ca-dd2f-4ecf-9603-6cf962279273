package com.ybda.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ybda.model.entity.SysOrgUnits;
import com.ybda.service.SysOrgUnitsService;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 组织
 */
@RestController
@RequestMapping("/sysOrgUnits")
@RequiredArgsConstructor
public class SysOrgUnitsController {
    @Autowired
    SysOrgUnitsService sysOrgUnitsService;

    /**
     * 获取组织/部门/子部门管理树结构数据
     * @return
     */
    @SaCheckPermission("sys:org:units:view")
    @GetMapping("/tree")
    public SaResult tree() {
        return sysOrgUnitsService.queryAllOrgUnitsListConvertToTree();
    }
    /**
     * 获取组织/部门/子部门管理列表
     * @return
     */
    @SaCheckPermission("sys:org:units:view")
    @GetMapping("/getOrgUnitsList")
    public SaResult getOrgUnitsList(@RequestParam(required = false) String name,
                                   @RequestParam(required = false) String status,
                                   @RequestParam(defaultValue = "1") Integer current,
                                   @RequestParam(defaultValue = "10") Integer size) {
        return sysOrgUnitsService.page(name, status, current, size);
    }
    /**
     * 新增组织/部门/子部门
     * @return
     */
    @SaCheckPermission("sys:org:units:add")
    @PostMapping("/addOrgUnits")
    public SaResult addOrgUnits(@RequestBody SysOrgUnits orgUnits) {
        return sysOrgUnitsService.save(orgUnits) ? SaResult.ok("新增成功") : SaResult.error("新增失败");
    }
    /**
     * 修改组织/部门/子部门
     * @return
     */
    @SaCheckPermission("sys:org:units:update")
    @PostMapping("/updateOrgUnits")
    public SaResult updateOrgUnits(@RequestBody SysOrgUnits orgUnits) {
        return sysOrgUnitsService.updateById(orgUnits) ? SaResult.ok("修改成功") : SaResult.error("修改失败");
    }
    /**
     * 删除组织/部门/子部门
     * @return
     */
    @SaCheckPermission("sys:org:units:delete")
    @DeleteMapping("/deleteOrgUnits")
    public SaResult deleteOrgUnits(@RequestParam Integer id) {
        long count = sysOrgUnitsService.count(new LambdaQueryWrapper<SysOrgUnits>().eq(SysOrgUnits::getParentId, id));
        if (count > 0){
            return SaResult.error("删除失败，该组织/部门/子部门下有子节点");
        }
        return sysOrgUnitsService.removeById(id) ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }
    /**
     * 批量删除组织/部门/子部门
     * @return
     */
    @SaCheckPermission("sys:org:units:delete")
    @DeleteMapping("/deleteOrgUnitsBatch")
    public SaResult deleteOrgUnitsBatch(@RequestBody List<Integer> ids) {
        return sysOrgUnitsService.removeByIds(ids) ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }
    /**
     * 获取组织/部门/子部门详情
     * @return
     */
    @SaCheckPermission("sys:org:units:view")
    @GetMapping("/getOrgUnitsById")
    public SaResult getOrgUnitsById(@RequestParam Integer id) {
        return SaResult.data(sysOrgUnitsService.getById(id));
    }
}
