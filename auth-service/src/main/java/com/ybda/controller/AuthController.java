package com.ybda.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.dto.LoginDTO;
import com.ybda.model.entity.SysUser;
import com.ybda.service.SysUserService;
import com.ybda.utils.MathCaptchaUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 认证接口
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final MathCaptchaUtils mathCaptchaUtils;
    @Autowired
    private SysUserService sysUserService;
    /**
     * 获取验证码
     */
    @GetMapping("/captcha")
    public SaResult getCaptcha() {
        String uuid = UUID.randomUUID().toString();
        MathCaptchaUtils.CaptchaResult captchaResult = mathCaptchaUtils.generateCaptcha(uuid);
        Map<String, String> data = new HashMap<>();
        data.put("uuid", uuid);
        data.put("image", captchaResult.getImageBase64());
        return SaResult.data(data);
    }
    /**
     * 登录
     */
    @PostMapping("/login")
    public SaResult login(@RequestBody LoginDTO loginDTO) {
        // 验证验证码
//        if (!mathCaptchaUtils.verify(loginDTO.getUuid(), loginDTO.getCaptcha())) {
//            return SaResult.error("验证码错误");
//        }
        // TODO: 验证用户名密码
        // 这里需要实现用户认证逻辑
        SysUser user = sysUserService.selectOneByUsername(loginDTO);
        // 判断用户是否存在
        if (user == null) {
            return SaResult.error("用户名或密码错误");
        }
        // 第1步，先登录上
        StpUtil.login(user.getId());
        // 第2步，获取 Token  相关参数
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        // 第3步，返回给前端
        return SaResult.data(tokenInfo);
    }
    /**
     * 登出
     */
    @PostMapping("/logout")
    public SaResult logout() {
        StpUtil.logout();
        return SaResult.ok();
    }
    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/getUserInfo")
    public SaResult getUserInfo(String token) {
        // 获取：当前账号id
        long loginIdAsLong = StpUtil.getLoginIdAsLong();
        // 获取：当前账号所拥有的权限集合
        List<String> permissionList = StpUtil.getPermissionList();
        // 获取：当前账号所拥有的角色集合
        List<String> roleList = StpUtil.getRoleList();
        // 获取：当前账号的token信息
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        SysUser user = sysUserService.selectByPrimaryKey(loginIdAsLong);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("tokenInfo", tokenInfo);
        hashMap.put("user", user);
        hashMap.put("roleList", roleList);
        hashMap.put("permissionList", permissionList);
        // 第3步，返回给前端
        return SaResult.data(hashMap);
    }
} 