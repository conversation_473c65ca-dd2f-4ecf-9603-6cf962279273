package com.ybda.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.dto.SysRolePermissionDTO;
import com.ybda.model.entity.SysPermission;
import com.ybda.service.PermissionCacheService;
import com.ybda.service.SysPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
/**
 * 按钮接口
 */
@RestController
@RequestMapping("/permission")
@RequiredArgsConstructor
public class SysPermissionController {
    @Autowired
    private final SysPermissionService sysPermissionService;

    @Autowired
    private PermissionCacheService permissionCacheService;

    /**
     * 根据角色ID获取对应的按钮权限
     *
     * @return
     */
    @SaCheckPermission("sys:permission:view")
    @GetMapping("/getPermissionList")
    public SaResult getPermissionList(Integer roleId) {
        return sysPermissionService.selectPermissionList(roleId);
    }

    /**
     * 修改角色的按钮权限
     */
    @SaCheckPermission("sys:role:permission:add")
    @PostMapping("/updateRolePermission")
    public SaResult updateRolePermission(@RequestBody SysRolePermissionDTO sysRolePermissionDTO) {
        SaResult result = sysPermissionService.updateRolePermission(sysRolePermissionDTO);
        if (result.getCode() == 200) {
            // 角色权限修改成功，清除所有用户权限缓存
            permissionCacheService.clearAllUserPermissionsCache();
        }
        return result;
    }
    /**
     * 根据页面ID获取对应的按钮权限
     *
     * @return
     */
    @SaCheckPermission("sys:permission:view")
    @GetMapping("/getPermissionListByPageId")
    public SaResult getPermissionListByPageId(@RequestParam(required = false) String menuId,
                                              @RequestParam(defaultValue = "1") Integer current,
                                              @RequestParam(defaultValue = "10") Integer size) {
        return sysPermissionService.selectPermissionListByPageId(menuId, current, size);
    }

    /**
     * 获取所有权限注解
     */
    @SaCheckPermission("sys:permission:view")
    @GetMapping("/getAllPermissionAnnotations")
    public SaResult getAllPermissionAnnotations() {
//        Map<String, String> permissions = permissionDocScanner.getPermissionDocMap();
//        return SaResult.data(permissions);
        return SaResult.ok().setData(null);
    }

    /**
     * 添加权限
     */
    @SaCheckPermission("sys:permission:add")
    @PostMapping("/addPermission")
    public SaResult addPermission(@RequestBody SysPermission sysPermission) {
        boolean result = sysPermissionService.save(sysPermission);
        if (result) {
            // 权限添加成功，清除所有用户权限缓存
            permissionCacheService.clearAllUserPermissionsCache();
        }
        return result ? SaResult.ok("添加成功") : SaResult.error("添加失败");
    }

    /**
     * 修改权限
     */
    @SaCheckPermission("sys:permission:update")
    @PostMapping("/updatePermission")
    public SaResult updatePermission(@RequestBody SysPermission sysPermission) {
        boolean result = sysPermissionService.updateById(sysPermission);
        if (result) {
            // 权限修改成功，清除所有用户权限缓存
            permissionCacheService.clearAllUserPermissionsCache();
        }
        return result ? SaResult.ok("修改成功") : SaResult.error("修改失败");
    }

    /**
     * 删除权限
     */
    @SaCheckPermission("sys:permission:delete")
    @PostMapping("/deletePermission")
    public SaResult deletePermission(@RequestParam Integer id) {
        boolean result = sysPermissionService.removeById(id);
        if (result) {
            // 权限删除成功，清除所有用户权限缓存
            permissionCacheService.clearAllUserPermissionsCache();
        }
        return result ? SaResult.ok("删除成功") : SaResult.error("删除失败");
    }

}
