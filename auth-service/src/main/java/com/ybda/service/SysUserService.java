package com.ybda.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ybda.model.dto.LoginDTO;
import com.ybda.model.dto.SysUserAddDTO;
import com.ybda.model.dto.SysUserUpdateDTO;
import com.ybda.model.entity.SysUser;

import java.util.List;

public interface SysUserService extends IService<SysUser> {


    SysUser selectOneByUsername(LoginDTO loginDTO);

    SysUser selectByPrimaryKey(Long id);

//    SaResult list(String userName, String realName, String userPhone, String userEmail, String userGender, Integer status, Integer curPage, Integer pageSize);
//
//    SaResult updateUser(User user);

    SaResult addUser(SysUserAddDTO sysUserAddDTO);

    SaResult getUserManageList(String userName, String realName, String userEmail, List<Integer> orgIds, Integer current, Integer size);

    SaResult getUserById(Integer id);

    SaResult updateUser(SysUserUpdateDTO sysUserUpdateDTO);
}

