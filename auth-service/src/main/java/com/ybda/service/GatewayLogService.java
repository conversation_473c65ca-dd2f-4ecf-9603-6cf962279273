package com.ybda.service;

import cn.dev33.satoken.util.SaResult;
import com.ybda.model.dto.GatewayLogQueryDTO;
import com.ybda.model.entity.GatewayLog;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 网关日志服务接口
 */
public interface GatewayLogService {
    
    /**
     * 分页查询网关日志
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    SaResult getGatewayLogList(GatewayLogQueryDTO queryDTO);
    
    /**
     * 根据ID查询网关日志详情
     * @param id 日志ID
     * @return 日志详情
     */
    SaResult getGatewayLogById(String id);
}
