package com.ybda.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ybda.model.entity.SysMenu;

public interface SysMenuService extends IService<SysMenu>{


    SaResult tree();

    SaResult getEditMenuInfo(Integer id);

    SaResult getAllPages();

    SaResult queryMenuPermissionList();

    SaResult getMenuPermissionByRoleId(Integer roleId);
}
