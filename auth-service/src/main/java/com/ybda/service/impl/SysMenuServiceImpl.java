package com.ybda.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ybda.mapper.SysMenuMapper;
import com.ybda.mapper.SysPermissionMapper;
import com.ybda.mapper.SysRolePermissionMapper;
import com.ybda.model.entity.SysMenu;
import com.ybda.model.entity.SysPermission;
import com.ybda.model.entity.SysRolePermission;
import com.ybda.model.vo.SysMenuPermissionVO;
import com.ybda.model.vo.SysMenuTreeVO;
import com.ybda.service.SysMenuService;
import com.ybda.utils.CglibUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {
    @Autowired
    SysMenuMapper sysMenuMapper;
    @Autowired
    SysPermissionMapper sysPermissionMapper;
    @Autowired
    SysRolePermissionMapper sysRolePermissionMapper;
    @Override
    public SaResult tree() {
        // 查询所有数据
        QueryWrapper<SysMenu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_constant",0);
        List<SysMenu> MenuLise = sysMenuMapper.selectList(queryWrapper);
        return SaResult.data(initMenuChildren(0L, MenuLise));
    }

    @Override
    public SaResult getEditMenuInfo(Integer id) {
        SysMenu menu = sysMenuMapper.selectById(id);
        return SaResult.data(menu);
    }

    @Override
    public SaResult getAllPages() {
        List<String>  allPages =sysMenuMapper.getAllPages();
        return SaResult.data(allPages);
    }

    @Override
    public SaResult queryMenuPermissionList() {
        // 查询所有菜单信息
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getStatus, "1")
                .eq(SysMenu::getType, "2")
                .eq(SysMenu::getIsConstant, 0)
                .orderByAsc(SysMenu::getSort);
        List<SysMenu> menus = sysMenuMapper.selectList(queryWrapper);
        // 查询所有权限信息
        LambdaQueryWrapper<SysPermission> queryWrapper1 = new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getStatus, "1");
        List<SysPermission> permission = sysPermissionMapper.selectList(queryWrapper1);

        // 根据菜单Id分组
        Map<Long, List<SysPermission>> menuIdPermissionMap = permission.stream()
                .collect(Collectors.groupingBy(SysPermission::getMenuId));
        // 组装数据
        List<SysMenuPermissionVO> MenuPermissionVOS = new ArrayList<>();
        // 遍历菜单信息
        menus.stream().map(menu -> {
            // 构建菜单权限对象
            SysMenuPermissionVO build = SysMenuPermissionVO.builder()
                    .menuId(menu.getId())
                    .menuName(menu.getName())
                    .i18nKey(menu.getI18nKey())
                    .build();
            // 根据菜单Id获取权限信息
            List<SysPermission> permissions = menuIdPermissionMap.getOrDefault(menu.getId(),new ArrayList<>());
            // 按照排序值排序
            permissions.sort(Comparator.comparing(SysPermission::getSort));
            // 构建按钮权限对象
            build.setButtons(CglibUtil.convertList(permissions, SysMenuPermissionVO.Button::new));
            return build;
        }).forEach(MenuPermissionVOS::add);
        return SaResult.data(MenuPermissionVOS);
    }

    @Override
    public SaResult getMenuPermissionByRoleId(Integer roleId) {
        LambdaQueryWrapper<SysRolePermission> queryWrapper = new LambdaQueryWrapper<SysRolePermission>()
                .eq(SysRolePermission::getRoleId, roleId);
        List<SysRolePermission> rolePermissions = sysRolePermissionMapper.selectList(queryWrapper);
        return SaResult.data(rolePermissions.stream().map(SysRolePermission::getPermissionId).toList());
    }


    /**
     * 组装路由结构
     *
     * @param parentId 父Id
     * @param sysMenus 菜单集合
     * @return {@linkplain SysMenuTreeVO} 菜单管理对象集合
     */
    private static List<SysMenuTreeVO> initMenuChildren(Long parentId, List<SysMenu> sysMenus) {
        // 根据 parentId 获取菜单列表
        List<SysMenu> parentMenuList = sysMenus.stream()
                .filter(item -> item.getParentId().equals(parentId)).toList();
        List<SysMenuTreeVO> menuPageVOList =new ArrayList<>();
        parentMenuList.forEach(item -> {
            SysMenuTreeVO menuPageVO = CglibUtil.convertObj(item, SysMenuTreeVO::new);
            menuPageVO.setChildren(initMenuChildren(item.getId(), sysMenus));
            menuPageVOList.add(menuPageVO);
        });
        // 按照排序值排序
        menuPageVOList.sort(Comparator.comparing(SysMenuTreeVO::getSort));
        return menuPageVOList;
    }

}
