package com.ybda.service.impl;

import cn.dev33.satoken.util.SaResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ybda.mapper.SysUserMapper;
import com.ybda.model.dto.GatewayLogQueryDTO;
import com.ybda.model.entity.GatewayLog;
import com.ybda.model.entity.SysUser;
import com.ybda.model.vo.GatewayLogListVO;
import com.ybda.model.vo.GatewayLogVO;
import com.ybda.repository.GatewayLogRepository;
import com.ybda.service.GatewayLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * 网关日志服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GatewayLogServiceImpl implements GatewayLogService {
    
    private final GatewayLogRepository gatewayLogRepository;
    private final MongoTemplate mongoTemplate;
    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Override
    public SaResult getGatewayLogList(GatewayLogQueryDTO queryDTO) {
        try {
            // 构建查询条件
            Query query = buildQuery(queryDTO);
            
            // 构建分页和排序
            Pageable pageable = buildPageable(queryDTO);

            // 执行查询 - 只查询列表页面需要的字段
            long total = mongoTemplate.count(query, GatewayLog.class);

            // 添加字段投影，只查询需要的字段
            query.fields()
                    .include("_id")
                    .include("user_id")
                    .include("schema")
                    .include("request_method")
                    .include("referer")
                    .include("request_path")
                    .include("status_code")
                    .include("ip")
                    .include("target_server")
                    .include("execute_time")
                    .include("request_time");

            query.with(pageable);
            List<GatewayLog> logs = mongoTemplate.find(query, GatewayLog.class);

            // 转换为轻量级VO，无需解压数据
            List<GatewayLogListVO> voList = logs.stream()
                    .map(this::convertToListVO)
                    .collect(Collectors.toList());
            
            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", voList);
            result.put("total", total);
            result.put("current", queryDTO.getCurrent());
            result.put("size", queryDTO.getSize());
            result.put("pages", (total + queryDTO.getSize() - 1) / queryDTO.getSize());
            
            return SaResult.data(result);
        } catch (Exception e) {
            log.error("查询网关日志失败", e);
            return SaResult.error("查询网关日志失败: " + e.getMessage());
        }
    }
    
    @Override
    public SaResult getGatewayLogById(String id) {
        try {
            Optional<GatewayLog> optional = gatewayLogRepository.findById(id);
            if (optional.isPresent()) {
                GatewayLogVO vo = convertToVO(optional.get());
                return SaResult.data(vo);
            } else {
                return SaResult.error("日志记录不存在");
            }
        } catch (Exception e) {
            log.error("查询网关日志详情失败", e);
            return SaResult.error("查询网关日志详情失败: " + e.getMessage());
        }
    }

    /**
     * 构建查询条件
     */
    private Query buildQuery(GatewayLogQueryDTO queryDTO) {
        Query query = new Query();
        
        if (StrUtil.isNotBlank(queryDTO.getRequestPath())) {
            query.addCriteria(Criteria.where("request_path").regex(queryDTO.getRequestPath(), "i"));
        }
        
        if (StrUtil.isNotBlank(queryDTO.getRequestMethod())) {
            query.addCriteria(Criteria.where("request_method").is(queryDTO.getRequestMethod()));
        }
        
        if (StrUtil.isNotBlank(queryDTO.getTargetServer())) {
            query.addCriteria(Criteria.where("target_server").regex(queryDTO.getTargetServer(), "i"));
        }
        
        if (StrUtil.isNotBlank(queryDTO.getIp())) {
            query.addCriteria(Criteria.where("ip").regex(queryDTO.getIp(), "i"));
        }
        
        if (StrUtil.isNotBlank(queryDTO.getUserId())) {
            query.addCriteria(Criteria.where("user_id").is(queryDTO.getUserId()));
        }
        
        if (queryDTO.getStatusCode() != null) {
            query.addCriteria(Criteria.where("status_code").is(queryDTO.getStatusCode()));
        }
        
        if (queryDTO.getMinExecuteTime() != null && queryDTO.getMaxExecuteTime() != null) {
            query.addCriteria(Criteria.where("execute_time").gte(queryDTO.getMinExecuteTime()).lte(queryDTO.getMaxExecuteTime()));
        } else if (queryDTO.getMinExecuteTime() != null) {
            query.addCriteria(Criteria.where("execute_time").gte(queryDTO.getMinExecuteTime()));
        }
        
        if (queryDTO.getStartTime() != null && queryDTO.getEndTime() != null) {
            query.addCriteria(Criteria.where("create_time").gte(queryDTO.getStartTime()).lte(queryDTO.getEndTime()));
        }

//        if (queryDTO.getHasError() != null && queryDTO.getHasError()) {
//            query.addCriteria(Criteria.where("error_message").ne(null).ne(""));
//        }
//
//        if (StrUtil.isNotBlank(queryDTO.getUserAgent())) {
//            query.addCriteria(Criteria.where("user_agent").regex(queryDTO.getUserAgent(), "i"));
//        }
        
        return query;
    }
    
    /**
     * 构建分页和排序
     */
    private Pageable buildPageable(GatewayLogQueryDTO queryDTO) {
        Sort.Direction direction = "asc".equalsIgnoreCase(queryDTO.getSortDirection())
                ? Sort.Direction.ASC : Sort.Direction.DESC;
        Sort sort = Sort.by(direction, queryDTO.getSortField());
        return PageRequest.of(queryDTO.getCurrent() - 1, queryDTO.getSize(), sort);
    }

    /**
     * 将GatewayLog转换为轻量级的GatewayLogListVO，用于列表查询
     * @param log 原始日志对象
     * @return 轻量级VO对象
     */
    private GatewayLogListVO convertToListVO(GatewayLog log) {
        GatewayLogListVO gatewayLogListVO = BeanUtil.copyProperties(log, GatewayLogListVO.class);
        String id = gatewayLogListVO.getUserId();
        if (!StringUtils.isEmpty(id)) {
            SysUser sysUser = sysUserMapper.selectById(id);
            gatewayLogListVO.setUserName(sysUser.getUserName());
        }
        return gatewayLogListVO;
    }

    /**
     * 将GatewayLog转换为GatewayLogVO，并解压压缩的数据
     * @param log 原始日志对象
     * @return 解压后的VO对象
     */
    private GatewayLogVO convertToVO(GatewayLog log) {
        GatewayLogVO vo = BeanUtil.copyProperties(log, GatewayLogVO.class);
        // 解压请求体数据
        if (StrUtil.isNotBlank(vo.getRequestBody())) {
            try {
                String decompressedRequestBody = smartDecompress(vo.getRequestBody());
                vo.setRequestBody(decompressedRequestBody);
            } catch (Exception ignored) {
            }
        }
        // 解压响应数据
        if (StrUtil.isNotBlank(vo.getResponseData())) {
            try {
                String decompressedResponseData = smartDecompress(vo.getResponseData());
                vo.setResponseData(decompressedResponseData);
            } catch (Exception ignored) {
            }
        }
        String id = vo.getUserId();
        if (!StringUtils.isEmpty(id)) {
            SysUser sysUser = sysUserMapper.selectById(id);
            vo.setUserName(sysUser.getUserName());
        }
        return vo;
    }

    /**
     * 智能解压缩：自动检测是否为压缩数据
     * @param data 可能压缩的数据
     * @return 解压缩后的原始数据
     */
    private String smartDecompress(String data) {
        if (data == null) return null;

        // 检查是否为压缩数据（以GZIP:开头）
        if (data.startsWith("GZIP:")) {
            try {
                return decompress(data);
            } catch (Exception e) {
                log.warn("⚠️ 数据解压缩失败，返回原数据: {}", e.getMessage());
                return data;
            }
        }

        return data;  // 非压缩数据直接返回
    }

    /**
     * Base64解码 + GZIP解压缩
     * @param compressedData 压缩的数据
     * @return 解压缩后的原始字符串
     * @throws Exception 解压缩异常
     */
    private String decompress(String compressedData) throws Exception {
        if (compressedData == null || !compressedData.startsWith("GZIP:")) {
            return compressedData;
        }

        String base64Data = compressedData.substring(5); // 去掉"GZIP:"前缀
        byte[] compressed = Base64.getDecoder().decode(base64Data);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPInputStream gzis = new GZIPInputStream(new ByteArrayInputStream(compressed))) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
        }

        return baos.toString(StandardCharsets.UTF_8);
    }
}
