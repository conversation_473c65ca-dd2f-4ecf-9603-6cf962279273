package com.ybda.service.impl;

import com.ybda.mapper.SysMenuMapper;
import com.ybda.model.dto.SysMenuRouteDTO;
import com.ybda.model.dto.SysUserRouteDTO;
import com.ybda.model.entity.SysMenu;
import com.ybda.service.SysRouteService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class SysRouteServiceImpl implements SysRouteService {

    @Autowired
    private SysMenuMapper sysMenuMapper;

    @Override
    public List<SysMenuRouteDTO> getConstantRoutes() {
        List<SysMenu> menuList = sysMenuMapper.selectConstantRoutes();
        return buildMenuTree(menuList);
    }

    @Override
    public SysUserRouteDTO getUserRoutes(Long userId) {
        SysUserRouteDTO userRoute = new SysUserRouteDTO();

        try {
            // 获取用户菜单
            List<SysMenu> menuList = sysMenuMapper.selectUserRoutes(userId);
            List<SysMenuRouteDTO> menus = buildMenuTree(menuList);

            // 获取用户权限
            List<String> permissions = sysMenuMapper.selectUserPermissions(userId);

            // 确保不返回null
            userRoute.setMenus(menus != null ? menus : new ArrayList<>());
            userRoute.setPermissions(permissions != null ? permissions : new ArrayList<>());
        } catch (Exception e) {
//            log.error("获取用户路由信息失败", e);
            // 出现异常时也确保返回空数组而不是null
            userRoute.setMenus(new ArrayList<>());
            userRoute.setPermissions(new ArrayList<>());
        }

        return userRoute;
    }

    /**
     * 构建菜单树
     *
     * @param menuList 菜单列表
     * @return 树形结构的菜单列表
     */
    private List<SysMenuRouteDTO> buildMenuTree(List<SysMenu> menuList) {
        // 防止传入null
        if (menuList == null || menuList.isEmpty()) {
            return new ArrayList<>();
        }

        List<SysMenuRouteDTO> returnList = new ArrayList<>();

        // 将菜单列表转换为DTO
        List<SysMenuRouteDTO> menuDtoList = new ArrayList<>();
        for (SysMenu menu : menuList) {
            SysMenuRouteDTO menuDto = new SysMenuRouteDTO();
            BeanUtils.copyProperties(menu, menuDto);
            // 确保children不是null
            menuDto.setChildren(new ArrayList<>());
            menuDtoList.add(menuDto);
        }

        // 构建树形结构 - 使用Map提高效率
        Map<Long, SysMenuRouteDTO> menuMap = menuDtoList.stream()
                .collect(Collectors.toMap(SysMenuRouteDTO::getId, menu -> menu));

        for (SysMenuRouteDTO menu : menuDtoList) {
            // 处理顶级节点
            if (menu.getParentId() == null || menu.getParentId() == 0) {
                returnList.add(menu);
            } else {
                // 处理子节点
                SysMenuRouteDTO parentMenu = menuMap.get(menu.getParentId());
                if (parentMenu != null) {
                    if (parentMenu.getChildren() == null) {
                        parentMenu.setChildren(new ArrayList<>());
                    }
                    parentMenu.getChildren().add(menu);
                } else {
                    // 如果找不到父节点，当作顶级节点处理
                    returnList.add(menu);
                }
            }
        }

        return returnList;
    }
}
