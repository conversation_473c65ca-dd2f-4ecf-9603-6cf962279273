package com.ybda.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ybda.mapper.SysPermissionMapper;
import com.ybda.mapper.SysRolePermissionMapper;
import com.ybda.model.dto.SysRolePermissionDTO;
import com.ybda.model.entity.SysPermission;
import com.ybda.model.entity.SysRolePermission;
import com.ybda.service.SysPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public  class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements SysPermissionService {
    @Autowired
    private SysRolePermissionMapper sysRolePermissionMapper;
    @Autowired
    private SysPermissionMapper sysPermissionMapper;

    @Override
    public SaResult selectPermissionList(Integer roleId) {
        List<Integer> permissionList = sysRolePermissionMapper.selectPermissionList(roleId);
        return SaResult.data(permissionList);
    }

    @Override
    public SaResult selectPermissionListByPageId(String menuId, Integer current, Integer size) {
        IPage<SysPermission> page = new Page<>(current, size);
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(menuId != null, "menu_id", menuId);
        queryWrapper.orderByAsc("sort");
        IPage<SysPermission> permissionIPage = sysPermissionMapper.selectPage(page, queryWrapper);
        return SaResult.data(permissionIPage);
    }

    @Override
    public SaResult updateRolePermission(SysRolePermissionDTO sysRolePermissionDTO) {
        List<Integer> permissionIds = sysRolePermissionDTO.getPermissionIds();
        Integer roleId = sysRolePermissionDTO.getRoleId();
        QueryWrapper<SysRolePermission> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        sysRolePermissionMapper.delete(queryWrapper);
        // 批量添加新的按钮权限
        List<SysRolePermission> rolePermissions = permissionIds.stream()
                .map(menuId -> {
                    SysRolePermission rolePermission = new SysRolePermission();
                    rolePermission.setRoleId(roleId.longValue());
                    rolePermission.setPermissionId(menuId.longValue());
                    return rolePermission;
                })
                .toList();
        sysRolePermissionMapper.insertBatchSomeColumn(rolePermissions);
        return SaResult.ok("角色按钮权限更新成功");
    }


}
