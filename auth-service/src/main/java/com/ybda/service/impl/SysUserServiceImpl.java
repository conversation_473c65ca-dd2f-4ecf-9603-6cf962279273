package com.ybda.service.impl;


import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ybda.mapper.SysUserMapper;
import com.ybda.mapper.SysUserOrgMapper;
import com.ybda.mapper.SysUserRoleMapper;
import com.ybda.model.dto.LoginDTO;
import com.ybda.model.dto.SysUserAddDTO;
import com.ybda.model.dto.SysUserUpdateDTO;
import com.ybda.model.entity.SysUser;
import com.ybda.model.entity.SysUserOrg;
import com.ybda.model.entity.SysUserRole;
import com.ybda.service.PermissionCacheService;
import com.ybda.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    @Autowired
    SysUserMapper sysUserMapper;
    @Autowired
    SysUserRoleMapper sysUserRoleMapper;;
    @Autowired
    SysUserOrgMapper SysUserOrgMapper;

    @Autowired
    PermissionCacheService permissionCacheService;

    @Override
    public SysUser selectOneByUsername(LoginDTO loginDTO) {
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name", loginDTO.getUserName());
        queryWrapper.eq("password", loginDTO.getPassword());
        return sysUserMapper.selectOne(queryWrapper);
    }

    @Override
    public SysUser selectByPrimaryKey(Long id) {
        return sysUserMapper.selectById(id);
    }

    @Override
    @Transactional
    public SaResult addUser(SysUserAddDTO sysUserAddDTO) {
        SysUser user = new SysUser();
        user.setUserGender(sysUserAddDTO.getUserGender());
        user.setUserName(sysUserAddDTO.getUserName());
        user.setRealName(sysUserAddDTO.getRealName());
        user.setUserPhone(sysUserAddDTO.getUserPhone());
        user.setUserEmail(sysUserAddDTO.getUserEmail());
        user.setPassword(sysUserAddDTO.getPassword());
        user.setStatus(sysUserAddDTO.getStatus());
        user.setCreateBy(sysUserAddDTO.getCreateBy());
        user.setUpdateBy(sysUserAddDTO.getUpdateBy());
        sysUserMapper.insert(user);
        List<Integer> userRoles = sysUserAddDTO.getUserRoles();
        userRoles.forEach(roleId -> {
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(user.getId());
            userRole.setRoleId(roleId);
            sysUserRoleMapper.insert(userRole);
        });
        List<Integer> userOrgUnits = sysUserAddDTO.getUserOrgUnits();
        userOrgUnits.forEach(orgId -> {
            SysUserOrg userOrg = new SysUserOrg();
            userOrg.setOrgId(orgId);
            userOrg.setUserId(user.getId());
            SysUserOrgMapper.insert(userOrg);
        });
        // 用户添加成功，由于分配了角色，需要清除该用户的权限缓存（如果之前有缓存的话）
        permissionCacheService.clearUserCache(user.getId());
        return SaResult.ok();
    }

    @Override
    public SaResult getUserManageList(String userName, String realName, String userEmail, List<Integer> orgIds, Integer current, Integer size) {
        // 处理 -1 判断
        boolean hasMinusOne = false;
        if (orgIds != null && orgIds.contains(-1)) {
            hasMinusOne = true;
            orgIds = orgIds.stream().filter(id -> id != -1).toList();
        }
        IPage<SysUser> iPage = new Page<>(current, size);
        List<SysUser> records;
        if (hasMinusOne) {
            // 查询没有组织的用户
            records = sysUserMapper.listUsersWithoutOrg(userName, realName, userEmail);
        } else if (orgIds == null || orgIds.isEmpty()) {
            // 没传组织ID，返回空结果
            records = Collections.emptyList();
        } else {
            // 查询指定组织下的用户
            records = sysUserMapper.listUsersWithOrgs(orgIds, userName, realName, userEmail);
        }

        iPage.setRecords(records);
        return SaResult.data(iPage);
    }

    @Override
    public SaResult getUserById(Integer id) {
        SysUser user = sysUserMapper.selectUser(id);
        return SaResult.data(user);
    }

    @Override
    @Transactional
    public SaResult updateUser(SysUserUpdateDTO sysUserUpdateDTO) {
        SysUser user = new SysUser();
        user.setId(sysUserUpdateDTO.getId());
        user.setUserGender(sysUserUpdateDTO.getUserGender());
        user.setUserName(sysUserUpdateDTO.getUserName());
        user.setRealName(sysUserUpdateDTO.getRealName());
        user.setUserPhone(sysUserUpdateDTO.getUserPhone());
        user.setUserEmail(sysUserUpdateDTO.getUserEmail());
        user.setStatus(sysUserUpdateDTO.getStatus());
        user.setUpdateBy(sysUserUpdateDTO.getUpdateBy());
        sysUserMapper.updateById(user);
        sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().eq("user_id", user.getId()));
        List<Integer> userRoles = sysUserUpdateDTO.getUserRoles();
        userRoles.forEach(roleId -> {
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(user.getId());
            userRole.setRoleId(roleId);
            sysUserRoleMapper.insert(userRole);
        });
        SysUserOrgMapper.delete(new QueryWrapper<SysUserOrg>().eq("user_id", user.getId()));
        List<Integer> userOrgUnits = sysUserUpdateDTO.getUserOrgUnits();
        userOrgUnits.forEach(orgId -> {
            SysUserOrg userOrg = new SysUserOrg();
            userOrg.setOrgId(orgId);
            userOrg.setUserId(user.getId());
            SysUserOrgMapper.insert(userOrg);
        });

        // 用户更新成功，角色可能发生变化，清除该用户的权限缓存
        permissionCacheService.clearUserCache(user.getId());

        return SaResult.ok();
    }

}

