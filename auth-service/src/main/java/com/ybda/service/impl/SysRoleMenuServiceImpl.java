package com.ybda.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ybda.mapper.SysRoleMenuMapper;
import com.ybda.model.dto.SysRoleMenuIdsDTO;
import com.ybda.model.entity.SysRoleMenu;
import com.ybda.service.SysRoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {
    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;
    @Override
    public SaResult getMenuListByRoleId(Integer roleId) {
        List<Integer> menuIds = sysRoleMenuMapper.selectMenuListByRoleId(roleId);
        List<String> stringMenuIds = menuIds.stream()
                .map(String::valueOf) // 将 Integer 转换为 String
                .toList(); // 收集为 List<String>
        return SaResult.data(stringMenuIds);
    }

    @Override
    public SaResult updateRoleMenuIds(SysRoleMenuIdsDTO sysRoleMenuIdsDTO) {
        Integer roleId = sysRoleMenuIdsDTO.getRoleId();
        List<Integer> menuIds = sysRoleMenuIdsDTO.getMenuIds();
         QueryWrapper<SysRoleMenu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        sysRoleMenuMapper.delete(queryWrapper);
        // 批量添加新的菜单权限
        List<SysRoleMenu> roleMenus = menuIds.stream()
                .map(menuId -> {
                    SysRoleMenu roleMenu = new SysRoleMenu();
                    roleMenu.setRoleId(roleId.longValue());
                    roleMenu.setMenuId(menuId.longValue());
                    return roleMenu;
                })
                .toList();

        // 使用 MyBatis-Plus 的批量插入功能
        sysRoleMenuMapper.insertBatchSomeColumn(roleMenus);

        return SaResult.ok("角色菜单权限更新成功");
    }
}
