package com.ybda.service.impl;

import cn.dev33.satoken.stp.StpInterface;
import com.ybda.mapper.SysUserMapper;
import com.ybda.service.PermissionCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 自定义权限加载接口实现类
 * 支持Redis缓存优化
 */
@Slf4j
@Component    // 保证此类被 SpringBoot 扫描，完成 Sa-Token 的自定义权限验证扩展
public class StpInterfaceImpl implements StpInterface {
    @Autowired
    SysUserMapper sysUserMapper;

    @Autowired
    PermissionCacheService permissionCacheService;

    /**
     * 返回一个账号所拥有的权限码集合
     * 优先从Redis缓存获取，缓存未命中时查询数据库并缓存结果
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        Integer userId = Integer.parseInt(loginId.toString());

        // 先从缓存获取
        List<String> permissions = permissionCacheService.getUserPermissionsFromCache(userId);
        if (permissions != null) {
            log.debug("从缓存获取用户权限: userId={}, permissions={}", userId, permissions);
            return permissions;
        }

        // 缓存未命中，查询数据库
        log.debug("缓存未命中，从数据库查询用户权限: userId={}", userId);
        permissions = sysUserMapper.selectPermissionList(userId);

        // 缓存查询结果
        permissionCacheService.cacheUserPermissions(userId, permissions);

        log.debug("从数据库获取并缓存用户权限: userId={}, permissions={}", userId, permissions);
        return permissions;
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     * 优先从Redis缓存获取，缓存未命中时查询数据库并缓存结果
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        Integer userId = Integer.parseInt(loginId.toString());

        // 先从缓存获取
        List<String> roles = permissionCacheService.getUserRolesFromCache(userId);
        if (roles != null) {
            log.debug("从缓存获取用户角色: userId={}, roles={}", userId, roles);
            return roles;
        }

        // 缓存未命中，查询数据库
        log.debug("缓存未命中，从数据库查询用户角色: userId={}", userId);
        roles = sysUserMapper.selectroleList(userId);

        // 缓存查询结果
        permissionCacheService.cacheUserRoles(userId, roles);

        log.debug("从数据库获取并缓存用户角色: userId={}, roles={}", userId, roles);
        return roles;
    }

}
