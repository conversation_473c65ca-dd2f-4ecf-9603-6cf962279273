package com.ybda.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ybda.mapper.SysRoleMapper;
import com.ybda.model.entity.SysRole;
import com.ybda.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Override
    public SaResult list(String roleName, String roleCode, Integer status, Integer current, Integer size) {
        IPage<SysRole> page = new Page<>(current, size);
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(roleName), "role_name", roleName);
        queryWrapper.like(StringUtils.isNotBlank(roleCode), "role_code", roleCode);
        queryWrapper.eq(status != null, "status", status);
        IPage<SysRole> roleIPage = sysRoleMapper.selectPage(page, queryWrapper);
        return SaResult.data(roleIPage);
    }
}
