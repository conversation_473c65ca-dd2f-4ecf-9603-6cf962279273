package com.ybda.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ybda.mapper.SysOrgUnitsMapper;
import com.ybda.model.entity.SysOrgUnits;
import com.ybda.model.vo.SysOrgUnitsTreeVO;
import com.ybda.service.SysOrgUnitsService;
import com.ybda.utils.CglibUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysOrgUnitsServiceImpl extends ServiceImpl<SysOrgUnitsMapper, SysOrgUnits> implements SysOrgUnitsService {
@Autowired
private SysOrgUnitsMapper orgUnitsMapper;
    @Override
    public SaResult queryAllOrgUnitsListConvertToTree() {
        //查询所以数据
                LambdaQueryWrapper<SysOrgUnits> queryWrapper = new LambdaQueryWrapper<SysOrgUnits>()
                .eq(SysOrgUnits::getStatus, "1")
                .orderByAsc(SysOrgUnits::getSort);
        List<SysOrgUnits> orgUnits = orgUnitsMapper.selectList(queryWrapper);
        // 按 parentId 分组，并确保每个分组内的列表按sort字段排序
        Map<Long, List<SysOrgUnits>> orgUnitsMap = orgUnits.stream()
                .collect(Collectors.groupingBy(SysOrgUnits::getParentId,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(SysOrgUnits::getSort))
                                        .collect(Collectors.toList()))));
        // 组装对应结构
        return SaResult.data(initOrgUnitsChild(0L, orgUnitsMap));
    }

    @Override
    public SaResult page(String name, String status, Integer current, Integer size) {
        // 1. 构建查询条件：只查顶级节点（parent_id = 0）
        LambdaQueryWrapper<SysOrgUnits> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrgUnits::getParentId, 0L);
        if (name != null && !name.isEmpty()) {
            queryWrapper.like(SysOrgUnits::getName, name);
        }
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq(SysOrgUnits::getStatus, status);
        }
        // 添加排序条件
        queryWrapper.orderByAsc(SysOrgUnits::getSort);

        // 2. 分页查询顶级节点
        IPage<SysOrgUnits> topPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(current, size);
        orgUnitsMapper.selectPage(topPage, queryWrapper);
        List<SysOrgUnits> topOrgUnits = topPage.getRecords();

        if (topOrgUnits.isEmpty()) {
            // 返回标准的空分页结果
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("current", current);
            emptyResult.put("size", size);
            emptyResult.put("pages", 0);
            emptyResult.put("total", 0);
            emptyResult.put("records", Collections.emptyList());
            return SaResult.data(emptyResult);
        }

        // 3. 查询所有子节点（递归或批量）
        List<Long> topLevelIds = topOrgUnits.stream()
                .map(SysOrgUnits::getId)
                .toList();
        List<SysOrgUnits> allOrgUnits = orgUnitsMapper.listAllDescendants(topLevelIds);
        // 对子节点按sort字段排序
        allOrgUnits = allOrgUnits.stream()
                .sorted(Comparator.comparing(SysOrgUnits::getSort))
                .toList();

        // 4. 按 parentId 分组，构建树结构所需数据源，并确保每个分组内的列表按sort字段排序
        Map<Long, List<SysOrgUnits>> orgUnitsMap = allOrgUnits.stream()
                .collect(Collectors.groupingBy(SysOrgUnits::getParentId,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(SysOrgUnits::getSort))
                                        .collect(Collectors.toList()))));

        // 5. 构建树形 VO 列表，并按sort排序
        List<SysOrgUnitsTreeVO> treeVOList = topOrgUnits.stream()
                .map(unit -> {
                    SysOrgUnitsTreeVO vo = CglibUtil.convertObj(unit, SysOrgUnitsTreeVO::new);
                    vo.setChildren(initOrgUnitsChild(unit.getId(), orgUnitsMap));
                    return vo;
                })
                .sorted(Comparator.comparing(SysOrgUnitsTreeVO::getSort))
                .toList();

        // 6. 构建返回结果，使用可变Map以避免不必要的限制
        Map<String, Object> result = new HashMap<>();
        result.put("current", current);
        result.put("size", size);
        result.put("pages", topPage.getPages());
        result.put("total", topPage.getTotal());
        result.put("records", treeVOList);

        return SaResult.data(result);
    }

    private static List<SysOrgUnitsTreeVO> initOrgUnitsChild(Long parentId, Map<Long, List<SysOrgUnits>> orgUnitsMap) {
        // 获取子单位
        List<SysOrgUnits> childOrgUnits = orgUnitsMap.get(parentId);
        if (CollectionUtils.isEmpty(childOrgUnits)) {
            return Collections.emptyList();
        }
        // 递归初始化子单位
        return childOrgUnits.stream()
                .map(unit -> {
                    SysOrgUnitsTreeVO orgUnitsTreeVO = CglibUtil.convertObj(unit, SysOrgUnitsTreeVO::new);
                    orgUnitsTreeVO.setChildren(initOrgUnitsChild(unit.getId(), orgUnitsMap));
                    return orgUnitsTreeVO;
                })
                .sorted(Comparator.comparing(SysOrgUnitsTreeVO::getSort))
                .toList();
    }
}
