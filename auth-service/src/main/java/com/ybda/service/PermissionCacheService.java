package com.ybda.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 权限缓存服务
 * 负责用户权限和角色的Redis缓存管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionCacheService {
    
    private final RedisUtils redisUtils;
    private final ObjectMapper objectMapper;
    
    // 缓存key前缀
    private static final String USER_PERMISSIONS_PREFIX = "user:permissions:";
    private static final String USER_ROLES_PREFIX = "user:roles:";
    
    // 缓存过期时间（30分钟）
    private static final long CACHE_EXPIRE_MINUTES = 30;
    
    /**
     * 获取用户权限缓存
     * @param userId 用户ID
     * @return 权限列表，如果缓存不存在返回null
     */
    public List<String> getUserPermissionsFromCache(Integer userId) {
        try {
            String key = USER_PERMISSIONS_PREFIX + userId;
            String cacheValue = redisUtils.get(key);
            
            if (cacheValue != null) {
                log.debug("从缓存获取用户权限: userId={}", userId);
                return objectMapper.readValue(cacheValue, new TypeReference<List<String>>() {});
            }
            
            log.debug("用户权限缓存未命中: userId={}", userId);
            return null;
        } catch (Exception e) {
            log.error("获取用户权限缓存失败: userId={}", userId, e);
            return null;
        }
    }
    
    /**
     * 缓存用户权限
     * @param userId 用户ID
     * @param permissions 权限列表
     */
    public void cacheUserPermissions(Integer userId, List<String> permissions) {
        try {
            String key = USER_PERMISSIONS_PREFIX + userId;
            String value = objectMapper.writeValueAsString(permissions != null ? permissions : new ArrayList<>());
            redisUtils.setEx(key, value, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.debug("缓存用户权限: userId={}, permissions={}", userId, permissions);
        } catch (Exception e) {
            log.error("缓存用户权限失败: userId={}", userId, e);
        }
    }
    
    /**
     * 获取用户角色缓存
     * @param userId 用户ID
     * @return 角色列表，如果缓存不存在返回null
     */
    public List<String> getUserRolesFromCache(Integer userId) {
        try {
            String key = USER_ROLES_PREFIX + userId;
            String cacheValue = redisUtils.get(key);
            
            if (cacheValue != null) {
                log.debug("从缓存获取用户角色: userId={}", userId);
                return objectMapper.readValue(cacheValue, new TypeReference<List<String>>() {});
            }
            
            log.debug("用户角色缓存未命中: userId={}", userId);
            return null;
        } catch (Exception e) {
            log.error("获取用户角色缓存失败: userId={}", userId, e);
            return null;
        }
    }
    
    /**
     * 缓存用户角色
     * @param userId 用户ID
     * @param roles 角色列表
     */
    public void cacheUserRoles(Integer userId, List<String> roles) {
        try {
            String key = USER_ROLES_PREFIX + userId;
            String value = objectMapper.writeValueAsString(roles != null ? roles : new ArrayList<>());
            redisUtils.setEx(key, value, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.debug("缓存用户角色: userId={}, roles={}", userId, roles);
        } catch (Exception e) {
            log.error("缓存用户角色失败: userId={}", userId, e);
        }
    }
    
    /**
     * 清除指定用户的权限和角色缓存
     * @param userId 用户ID
     */
    public void clearUserCache(Integer userId) {
        try {
            String permissionKey = USER_PERMISSIONS_PREFIX + userId;
            String roleKey = USER_ROLES_PREFIX + userId;
            
            redisUtils.delete(permissionKey);
            redisUtils.delete(roleKey);
            
            log.info("清除用户缓存: userId={}", userId);
        } catch (Exception e) {
            log.error("清除用户缓存失败: userId={}", userId, e);
        }
    }
    
    /**
     * 清除所有用户权限缓存（用于权限或角色变更时）
     */
    public void clearAllUserPermissionsCache() {
        try {
            Set<String> permissionKeys = redisUtils.keys(USER_PERMISSIONS_PREFIX + "*");
            Set<String> roleKeys = redisUtils.keys(USER_ROLES_PREFIX + "*");
            
            if (permissionKeys != null && !permissionKeys.isEmpty()) {
                redisUtils.delete(permissionKeys);
                log.info("清除所有用户权限缓存，数量: {}", permissionKeys.size());
            }
            
            if (roleKeys != null && !roleKeys.isEmpty()) {
                redisUtils.delete(roleKeys);
                log.info("清除所有用户角色缓存，数量: {}", roleKeys.size());
            }
        } catch (Exception e) {
            log.error("清除所有用户权限缓存失败", e);
        }
    }
    
    /**
     * 清除指定用户列表的缓存
     * @param userIds 用户ID列表
     */
    public void clearUsersCache(List<Integer> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        
        try {
            List<String> keysToDelete = new ArrayList<>();
            
            for (Integer userId : userIds) {
                keysToDelete.add(USER_PERMISSIONS_PREFIX + userId);
                keysToDelete.add(USER_ROLES_PREFIX + userId);
            }
            
            redisUtils.delete(keysToDelete);
            log.info("清除用户缓存: userIds={}", userIds);
        } catch (Exception e) {
            log.error("清除用户缓存失败: userIds={}", userIds, e);
        }
    }
}
