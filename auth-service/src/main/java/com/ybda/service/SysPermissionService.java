package com.ybda.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ybda.model.dto.SysRolePermissionDTO;
import com.ybda.model.entity.SysPermission;

public interface SysPermissionService extends IService<SysPermission>{


    SaResult selectPermissionList(Integer roleId);

    SaResult selectPermissionListByPageId(String menuId, Integer current, Integer size);

    SaResult updateRolePermission(SysRolePermissionDTO sysRolePermissionDTO);

}
