package com.ybda.repository;

import com.ybda.model.entity.GatewayLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 网关日志MongoDB Repository
 * 用于MongoDB数据访问操作
 */
@Repository
public interface GatewayLogRepository extends MongoRepository<GatewayLog, String> {
    
    /**
     * 根据请求路径查找日志
     * @param requestPath 请求路径
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByRequestPathContaining(String requestPath, Pageable pageable);
    
    /**
     * 根据用户ID查找日志
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByUserId(String userId, Pageable pageable);
    
    /**
     * 根据状态码查找日志
     * @param statusCode 状态码
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByStatusCode(Integer statusCode, Pageable pageable);
    
    /**
     * 根据时间范围查找日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据IP地址查找日志
     * @param ip IP地址
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByIp(String ip, Pageable pageable);
    
    /**
     * 查找执行时间超过指定值的日志
     * @param executeTime 执行时间阈值(毫秒)
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByExecuteTimeGreaterThan(Long executeTime, Pageable pageable);
    
    /**
     * 根据请求方法和路径查找日志
     * @param requestMethod 请求方法
     * @param requestPath 请求路径
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByRequestMethodAndRequestPathContaining(String requestMethod, String requestPath, Pageable pageable);
    
    /**
     * 根据目标服务查找日志
     * @param targetServer 目标服务
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    Page<GatewayLog> findByTargetServerContaining(String targetServer, Pageable pageable);
    
    /**
     * 查找有错误信息的日志
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    @Query("{'error_message': {$ne: null, $ne: ''}}")
    Page<GatewayLog> findByHasError(Pageable pageable);

    /**
     * 根据状态码范围查找日志
     * @param minStatusCode 最小状态码
     * @param maxStatusCode 最大状态码
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    @Query("{'status_code': {$gte: ?0, $lte: ?1}}")
    Page<GatewayLog> findByStatusCodeBetween(Integer minStatusCode, Integer maxStatusCode, Pageable pageable);

    /**
     * 根据执行时间范围查找日志
     * @param minExecuteTime 最小执行时间
     * @param maxExecuteTime 最大执行时间
     * @param pageable 分页参数
     * @return 日志分页列表
     */
    @Query("{'execute_time': {$gte: ?0, $lte: ?1}}")
    Page<GatewayLog> findByExecuteTimeBetween(Long minExecuteTime, Long maxExecuteTime, Pageable pageable);
    
    /**
     * 统计总记录数
     * @return 总记录数
     */
    long count();
    
    /**
     * 根据时间范围统计记录数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录数
     */
    long countByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据状态码统计记录数
     * @param statusCode 状态码
     * @return 记录数
     */
    long countByStatusCode(Integer statusCode);
}
