package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 组织/部门/子部门管理
 */
@Data
@TableName(value = "sys_org_units")
public class SysOrgUnits {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父组织/部门/子部门ID
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 组织/部门/子部门名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 组织/部门/子部门编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 组织/部门/子部门名称简写
     */
    @TableField(value = "abbr")
    private String abbr;

    /**
     * 组织/部门/子部门层级
     */
    @TableField(value = "`level`")
    private Integer level;

    /**
     * 祖先节点
     */
    @TableField(value = "ancestors")
    private String ancestors;

    /**
     * 组织/部门/子部门描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 排序值
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改用户
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改用户ID
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    @TableField(value = "`status`")
    private String status;
}