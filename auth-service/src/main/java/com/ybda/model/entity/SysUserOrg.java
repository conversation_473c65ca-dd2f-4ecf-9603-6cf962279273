package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户组织/部门/子部门管理
 */
@Data
@TableName(value = "sys_user_org")
public class SysUserOrg {
    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 组织/部门/子部门ID
     */
    @TableField(value = "org_id")
    private Integer orgId;
}