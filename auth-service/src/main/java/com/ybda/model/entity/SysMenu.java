package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 菜单管理
 */
@Data
@TableName(value = "sys_menu")
public class SysMenu {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父菜单ID
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 菜单类型 1:目录 2:子菜单 3:顶级菜单 4:特殊页面
     */
    @TableField(value = "`type`")
    private String type;

    /**
     * 菜单名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 多语言标题
     */
    @TableField(value = "i18n_key")
    private String i18nKey;

    /**
     * 路由名称
     */
    @TableField(value = "route_name")
    private String routeName;

    /**
     * 菜单路径
     */
    @TableField(value = "route_path")
    private String routePath;

    /**
     * 菜单图标
     */
    @TableField(value = "icon")
    private String icon;

    /**
     * 图标类型 1:iconify icon 2:local icon
     */
    @TableField(value = "icon_type")
    private String iconType;

    /**
     * 路由组件
     */
    @TableField(value = "component")
    private String component;

    /**
     * 缓存页面(Y:是,N:否)
     */
    @TableField(value = "keep_alive")
    private String keepAlive;

    /**
     * 是否隐藏(Y:是,N:否)
     */
    @TableField(value = "hide")
    private String hide;

    /**
     * 外部链接
     */
    @TableField(value = "href")
    private String href;

    /**
     * 排序值
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 支持多标签(Y:是,N:否)
     */
    @TableField(value = "multi_tab")
    private String multiTab;

    /**
     * 固定在页签中的序号
     */
    @TableField(value = "fixed_index_in_tab")
    private Integer fixedIndexInTab;

    /**
     * 内链URL
     */
    @TableField(value = "iframe_url")
    private String iframeUrl;

//    /**
//     * 路由查询参数
//     */
//    @TableField(value = "query")
//    private String query;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改用户
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改用户ID
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    @TableField(value = "`status`")
    private String status;



    /**
     * 是否常态路由(0:否,1:是)
     */
    @TableField(value = "is_constant")
    private Byte isConstant;
}