package com.ybda.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * 网关日志实体类
 * 用于记录通过网关的请求和响应信息到MongoDB
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "gateway_log")
public class GatewayLog {
    
    /**
     * 主键ID - MongoDB自动生成的ObjectId
     */
    @Id
    private String id;

    /**
     * 访问实例/目标服务
     */
    @Field("target_server")
    private String targetServer;

    /**
     * 请求路径 - 添加索引以提高查询性能
     */
    @Field("request_path")
    @Indexed
    private String requestPath;

    /**
     * 请求方法
     */
    @Field("request_method")
    @Indexed
    private String requestMethod;

    /**
     * 协议(http/https)
     */
    @Field("schema")
    private String schema;

    /**
     * 请求体内容
     */
    @Field("request_body")
    private String requestBody;

    /**
     * 响应数据
     */
    @Field("response_data")
    private String responseData;

    /**
     * 客户端IP地址
     */
    @Field("ip")
    private String ip;

    /**
     * 开始时间戳(毫秒)
     */
    @Field("start_time")
    private Long startTime;

    /**
     * 结束时间戳(毫秒)
     */
    @Field("end_time")
    private Long endTime;

    /**
     * 请求时间(格式化字符串)
     */
    @Field("request_time")
    private String requestTime;

    /**
     * 响应时间(格式化字符串)
     */
    @Field("response_time")
    private String responseTime;

    /**
     * 执行时间(ms)
     */
    @Field("execute_time")
    private Long executeTime;

    /**
     * 响应状态
     */
    @Field("status")
    private String status;

    /**
     * 响应状态码
     */
    @Field("status_code")
    private Integer statusCode;

    /**
     * 用户ID - 添加索引以提高查询性能
     */
    @Field("user_id")
    @Indexed
    private String userId;

//    /**
//     * 错误信息
//     */
//    @Field("error_message")
//    private String errorMessage;

//    /**
//     * 用户代理
//     */
//    @Field("user_agent")
//    private String userAgent;

    /**
     * 引用页面
     */
    @Field("referer")
    private String referer;

    /**
     * 创建时间 - 添加索引以提高时间范围查询性能
     * 使用@Builder.Default确保Builder模式下也能正确设置默认值
     */
    @Field("create_time")
    @Indexed
    @Builder.Default
    private LocalDateTime createTime = LocalDateTime.now();
}
