package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 权限(按钮)管理
 */
@Data
@TableName(value = "sys_permission")
public class SysPermission {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 菜单ID
     */
    @TableField(value = "menu_id")
    private Long menuId;

    /**
     * 菜单名称
     */
    @TableField(value = "menu_name")
    private String menuName;

    /**
     * 权限(按钮)名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 权限资源
     */
    @TableField(value = "`resource`")
    private String resource;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    @TableField(value = "`status`")
    private String status;
}