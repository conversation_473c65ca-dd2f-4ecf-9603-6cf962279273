package com.ybda.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 菜单路由数据传输对象
 */
@Data
public class SysMenuRouteDTO {
    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 菜单类型 1:目录 2:子菜单 3:顶级菜单 4:特殊页面
     */
    private String type;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 多语言标题
     */
    private String i18nKey;

    /**
     * 路由名称
     */
    private String routeName;

    /**
     * 菜单路径
     */
    private String routePath;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 图标类型 1:iconify icon 2:local icon
     */
    private String iconType;

    /**
     * 路由组件
     */
    private String component;

    /**
     * 缓存页面(Y:是,N:否)
     */
    private String keepAlive;

    /**
     * 是否隐藏(Y:是,N:否)
     */
    private String hide;

    /**
     * 外部链接
     */
    private String href;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 支持多标签(Y:是,N:否)
     */
    private String multiTab;

    /**
     * 固定在页签中的序号
     */
    private Integer fixedIndexInTab;

    /**
     * 内链URL
     */
    private String iframeUrl;

    /**
     * 路由查询参数
     */
    private String query;

    /**
     * 子菜单列表
     */
    private List<SysMenuRouteDTO> children;
}