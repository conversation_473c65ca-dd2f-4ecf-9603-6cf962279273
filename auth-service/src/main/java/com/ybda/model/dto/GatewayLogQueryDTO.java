package com.ybda.model.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 网关日志查询条件DTO
 */
@Data
public class GatewayLogQueryDTO {
    
    /**
     * 请求路径
     */
    private String requestPath;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 目标服务
     */
    private String targetServer;
    
    /**
     * 客户端IP地址
     */
    private String ip;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 响应状态码
     */
    private Integer statusCode;
    
    /**
     * 执行时间阈值(毫秒) - 查询执行时间大于此值的记录
     */
    private Long minExecuteTime;
    
    /**
     * 执行时间阈值(毫秒) - 查询执行时间小于此值的记录
     */
    private Long maxExecuteTime;
    
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
//    /**
//     * 是否有错误信息
//     */
//    private Boolean hasError;
    
//    /**
//     * 用户代理
//     */
//    private String userAgent;
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 排序字段
     */
    private String sortField = "create_time";
    
    /**
     * 排序方向 (asc/desc)
     */
    private String sortDirection = "desc";
}
