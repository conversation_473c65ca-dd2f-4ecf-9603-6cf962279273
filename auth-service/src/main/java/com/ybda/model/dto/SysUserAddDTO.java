package com.ybda.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SysUserAddDTO {
    /**
     * 用户名
     */
    private String userName;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 密码
     * 只在创建用户时进行验证
     */
    private String password;

    /**
     * 手机号
     */
    private String userPhone;

    /**
     * 邮箱
     */
    private String userEmail;

    /**
     * 性别（0女-1男）
     */
    private String userGender;

    /**
     * 用户状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 用户角色
     */
    private List<Integer> userRoles;

    /**
     * 用户组织
     */
    private List<Integer> userOrgUnits;
}
