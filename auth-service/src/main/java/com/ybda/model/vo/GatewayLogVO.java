package com.ybda.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 网关日志响应VO
 */
@Data
public class GatewayLogVO {
    
    /**
     * 主键ID
     */
    private String id;

    /**
     * 访问实例/目标服务
     */
    private String targetServer;

    /**
     * 请求路径
     */
    private String requestPath;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 协议(http/https)
     */
    private String schema;

    /**
     * 请求体内容
     */
    private String requestBody;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 客户端IP地址
     */
    private String ip;

    /**
     * 开始时间戳(毫秒)
     */
    private Long startTime;

    /**
     * 结束时间戳(毫秒)
     */
    private Long endTime;

    /**
     * 请求时间(格式化字符串)
     */
    private String requestTime;

    /**
     * 响应时间(格式化字符串)
     */
    private String responseTime;

    /**
     * 执行时间(ms)
     */
    private Long executeTime;

    /**
     * 响应状态
     */
    private String status;

    /**
     * 响应状态码
     */
    private Integer statusCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 错误信息
     */
    private String errorMessage;

//    /**
//     * 用户代理
//     */
//    private String userAgent;

    /**
     * 引用页面
     */
    private String referer;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
