package com.ybda.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 网关日志列表响应VO - 轻量级版本，只包含列表页面需要的字段
 */
@Data
public class GatewayLogListVO {
    
    /**
     * 主键ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 协议(http/https)
     */
    private String schema;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 引用页面
     */
    private String referer;

    /**
     * 请求路径
     */
    private String requestPath;

    /**
     * 响应状态码
     */
    private Integer statusCode;

    /**
     * 客户端IP地址
     */
    private String ip;

    /**
     * 访问实例/目标服务
     */
    private String targetServer;

    /**
     * 执行时间(ms)
     */
    private Long executeTime;

    /**
     * 请求时间(格式化字符串)
     */
    private String requestTime;
}