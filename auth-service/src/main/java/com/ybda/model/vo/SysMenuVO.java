package com.ybda.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SysMenuVO {

    @Serial
    private static final long serialVersionUID = 8285981486599719027L;
    private Long id;
    /**
     * 父菜单ID
     */
    private Long parentId;
    /**
     * 菜单类型 1:目录 2:子菜单 3:顶级菜单 4:特殊页面
     */
    private String type;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 多语言标题
     */
    private String i18nKey;
    /**
     * 路由名称
     */
    private String routeName;
    /**
     * 路由路径
     */
    private String routePath;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 图标类型 1:iconify icon 2:local icon
     */
    private String iconType;
    /**
     * 路由组件
     */
    private String component;
    /**
     * 缓存路由(Y:是,N:否)
     */
    private String keepAlive;
    /**
     * 是否隐藏(Y:是,N:否)
     */
    private String hide;
    /**
     * 外部链接
     */
    private String href;
    /**
     * 内嵌链接 Iframe URL
     */
    private String iframeUrl;
    /**
     * 排序值
     */
    private Integer sort;
    /**
     * 支持多标签(Y:是,N:否)
     */
    private String multiTab;
    /**
     * 固定在页签中的序号
     */
    private Integer fixedIndexInTab;
    /**
     * 路由查询参数 JSON 字符串
     */
    private String query;
    /**
     * 是否启用(0:禁用,1:启用)
     */
    private String status;

}
