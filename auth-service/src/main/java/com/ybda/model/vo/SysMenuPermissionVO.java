package com.ybda.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 菜单类型枚举类
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMenuPermissionVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4926000759340362039L;
    /**
     * 菜单ID
     */
    private Long menuId;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 多语言标题
     */
    private String i18nKey;
    /**
     * 权限按钮
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Button> buttons;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Button implements Serializable {

        @Serial
        private static final long serialVersionUID = 890980738928747062L;
        /**
         * 权限ID
         */
        private Long id;
        /**
         * 权限名称
         */
        private String name;
        /**
         * 权限标识
         */
        private String resource;
        /**
         * 权限描述
         */
        private String description;

    }

}

