package com.ybda.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SysMenuTreeVO extends SysMenuVO{
    @Serial
    private static final long serialVersionUID = -6337922157556940336L;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SysMenuTreeVO> children;
}
