package com.ybda.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.exception.SaTokenException;
import cn.dev33.satoken.util.SaResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器 - 处理SaToken权限相关异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public SaResult handleNotLoginException(NotLoginException e) {
        String type = e.getType();
        String message;
        int code;
        
        switch (type) {
            case NotLoginException.NOT_TOKEN:
                message = "未提供Token";
                code = 40101;
                break;
            case NotLoginException.INVALID_TOKEN:
                message = "无效Token";
                code = 40102;
                break;
            case NotLoginException.TOKEN_TIMEOUT:
                message = "Token已过期";
                code = 40103;
                break;
            case NotLoginException.BE_REPLACED:
                message = "Token已被顶下线";
                code = 40104;
                break;
            case NotLoginException.KICK_OUT:
                message = "Token已被踢下线";
                code = 40105;
                break;
            default:
                message = "当前会话未登录";
                code = 401;
                break;
        }
        
        log.warn("登录异常: {}", message);
        return SaResult.error(message).setCode(code);
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public SaResult handleNotPermissionException(NotPermissionException e) {
        String message = "无访问权限: " + e.getPermission();
        log.warn("权限异常: {}", message);
        return SaResult.error(message).setCode(403);
    }

    /**
     * 处理角色不足异常
     */
    @ExceptionHandler(NotRoleException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public SaResult handleNotRoleException(NotRoleException e) {
        String message = "无角色权限: " + e.getRole();
        log.warn("角色异常: {}", message);
        return SaResult.error(message).setCode(403);
    }

    /**
     * 处理其他SaToken异常
     */
    @ExceptionHandler(SaTokenException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public SaResult handleSaTokenException(SaTokenException e) {
        String message = "权限验证失败: " + e.getMessage();
        log.warn("SaToken异常: {}", message);
        return SaResult.error(message).setCode(403);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public SaResult handleException(Exception e) {
        log.error("系统异常", e);
        return SaResult.error("系统内部错误").setCode(500);
    }
} 