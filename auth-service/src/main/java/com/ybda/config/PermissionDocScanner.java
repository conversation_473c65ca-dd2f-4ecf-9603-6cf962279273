//package com.ybda.config;
//
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import jakarta.annotation.PostConstruct;
//import org.springframework.beans.BeansException;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.ApplicationContextAware;
//import org.springframework.stereotype.Component;
//import org.springframework.web.method.HandlerMethod;
//import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
//
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 权限文档扫描器：扫描所有控制器方法上的 SaCheckPermission 注解
// * 并将其与接口描述（@Operation.summary 或路径）进行映射
// */
//@Component
//public class PermissionDocScanner implements ApplicationContextAware {
//
//    private ApplicationContext applicationContext;
//    private Map<String, String> permissionDocMap = new HashMap<>();
//
//    @Override
//    public void setApplicationContext(ApplicationContext ctx) throws BeansException {
//        this.applicationContext = ctx;
//    }
//
//    /**
//     * 初始化方法：在 Bean 构造完成后执行扫描逻辑
//     */
//    @PostConstruct
//    public void init() {
//        RequestMappingHandlerMapping handlerMapping = applicationContext.getBean("requestMappingHandlerMapping", RequestMappingHandlerMapping.class);
//
//        permissionDocMap = handlerMapping.getHandlerMethods().entrySet().stream()
//                .filter(entry -> hasPermissionAnnotation(entry.getValue()))
//                .collect(Collectors.toMap(
//                        this::extractPermissionKey,
//                        this::extractOperationSummary,
//                        (existing, replacement) -> existing + " / " + replacement
//                ));
//    }
//
//    /**
//     * 判断方法是否有 SaCheckPermission 注解
//     */
//    private boolean hasPermissionAnnotation(HandlerMethod handlerMethod) {
//        return handlerMethod.getMethodAnnotation(SaCheckPermission.class) != null;
//    }
//
//    /**
//     * 提取权限 Key（SaCheckPermission.value()[0]）
//     */
//    private String extractPermissionKey(Map.Entry<?, HandlerMethod> entry) {
//        SaCheckPermission annotation = entry.getValue().getMethodAnnotation(SaCheckPermission.class);
//        return annotation != null && annotation.value().length > 0 ? annotation.value()[0] : "unknown";
//    }
//
//    /**
//     * 提取接口描述（优先取 Operation.summary，否则返回路径）
//     */
//    private String extractOperationSummary(Map.Entry<?, HandlerMethod> entry) {
//        HandlerMethod handlerMethod = entry.getValue();
//        Operation operation = handlerMethod.getMethodAnnotation(Operation.class);
//
//        if (operation != null && !operation.summary().isEmpty()) {
//            return operation.summary();
//        }
//
//        // fallback to path
//        return entry.getKey().toString(); // 可根据实际需求解析路径
//    }
//
//    /**
//     * 获取权限字符串到接口描述的映射
//     */
//    public Map<String, String> getPermissionDocMap() {
//        return Collections.unmodifiableMap(permissionDocMap);
//    }
//}