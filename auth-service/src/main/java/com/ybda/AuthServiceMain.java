package com.ybda;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableDiscoveryClient
@EnableCaching
@EnableScheduling
@SpringBootApplication
public class AuthServiceMain {
    public static void main(String[] args) {
        SpringApplication.run(AuthServiceMain.class, args);
    }
}