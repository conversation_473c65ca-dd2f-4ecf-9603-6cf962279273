<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysUserRole">
    <!--@mbg.generated-->
    <!--@Table sys_user_role-->
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    user_id, role_id
  </sql>

  <insert id="insertBatch">
    INSERT INTO sys_user_role (user_id, role_id)
    VALUES
    <foreach collection="list" item="ur" separator=",">
      (#{ur.userId}, #{ur.roleId})
    </foreach>
  </insert>
</mapper>