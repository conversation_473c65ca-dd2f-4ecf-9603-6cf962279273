<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysRolePermissionMapper">
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysRolePermission">
        <!--@mbg.generated-->
        <!--@Table sys_role_permission-->
        <id column="role_id" jdbcType="BIGINT" property="roleId" />
        <id column="permission_id" jdbcType="BIGINT" property="permissionId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        role_id, permission_id
    </sql>

    <select id="selectPermissionList" resultType="java.lang.Integer">
        select permission_id from sys_role_permission where role_id = #{roleId}
    </select>

    <insert id="insertBatchSomeColumn">
        INSERT INTO sys_role_permission (role_id, permission_id)
        VALUES
        <foreach collection="rolePermissions" item="rp" separator=",">
            (#{rp.roleId}, #{rp.permissionId})
        </foreach>
    </insert>
</mapper>