<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysMenuMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysMenu">
    <!--@mbg.generated-->
    <!--@Table sys_menu-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="i18n_key" jdbcType="VARCHAR" property="i18nKey" />
    <result column="route_name" jdbcType="VARCHAR" property="routeName" />
    <result column="route_path" jdbcType="VARCHAR" property="routePath" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="icon_type" jdbcType="VARCHAR" property="iconType" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="keep_alive" jdbcType="VARCHAR" property="keepAlive" />
    <result column="hide" jdbcType="VARCHAR" property="hide" />
    <result column="href" jdbcType="VARCHAR" property="href" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="multi_tab" jdbcType="VARCHAR" property="multiTab" />
    <result column="fixed_index_in_tab" jdbcType="INTEGER" property="fixedIndexInTab" />
    <result column="iframe_url" jdbcType="VARCHAR" property="iframeUrl" />
<!--    <result column="query" jdbcType="VARCHAR" property="query" />-->
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="is_constant" jdbcType="TINYINT" property="isConstant" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, parent_id, `type`, `name`, i18n_key, route_name, route_path, icon, icon_type, 
    component, keep_alive, hide, href, sort, multi_tab, fixed_index_in_tab, iframe_url, 
     create_user, create_user_id, create_time, update_user, update_user_id, update_time,
    `status`, is_constant
  </sql>

  <select id="getAllPages" resultType="java.lang.String">
    select route_name from sys_menu where type ='2' ORDER BY sort ASC
    </select>

  <!-- 查询常量路由 -->
  <select id="selectConstantRoutes" resultType="com.ybda.model.entity.SysMenu">
    SELECT *
    FROM sys_menu
    WHERE is_constant = 1
      AND status = '1'
    ORDER BY sort
  </select>

  <!-- 查询用户路由 -->
  <select id="selectUserRoutes" resultType="com.ybda.model.entity.SysMenu">
    SELECT DISTINCT m.*
    FROM sys_menu m
    INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
    INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
    WHERE ur.user_id = #{userId}
    AND m.status = '1'
    AND m.is_constant = 0
    ORDER BY m.sort
  </select>

  <!-- 查询用户权限 -->
  <select id="selectUserPermissions" resultType="java.lang.String">
    SELECT DISTINCT p.resource
    FROM sys_permission p
    INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
    INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = #{userId}
    AND p.status = '1'
  </select>
</mapper>