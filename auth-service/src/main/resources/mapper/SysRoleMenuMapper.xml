<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysRoleMenuMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysRoleMenu">
    <!--@mbg.generated-->
    <!--@Table sys_role_menu-->
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="menu_id" jdbcType="BIGINT" property="menuId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
     role_id, menu_id
  </sql>

  <select id="selectMenuListByRoleId" resultType="java.lang.Integer">
    SELECT menu_id FROM sys_role_menu
    WHERE role_id = #{roleId}
    </select>

  <insert id="insertBatchSomeColumn">
    INSERT INTO sys_role_menu (role_id, menu_id)
    VALUES
    <foreach collection="roleMenus" item="rm" separator=",">
      (#{rm.roleId}, #{rm.menuId})
    </foreach>
  </insert>
</mapper>