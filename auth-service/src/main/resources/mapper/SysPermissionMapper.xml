<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysPermissionMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysPermission">
    <!--@mbg.generated-->
    <!--@Table sys_permission-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="menu_id" jdbcType="BIGINT" property="menuId" />
    <result column="menu_name" jdbcType="VARCHAR" property="menuName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="resource" jdbcType="VARCHAR" property="resource" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, menu_id, menu_name, `name`, `resource`, description, sort, `status`
  </sql>
</mapper>