<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysUserOrgMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysUserOrg">
    <!--@mbg.generated-->
    <!--@Table sys_user_org-->
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
     user_id, org_id
  </sql>
</mapper>