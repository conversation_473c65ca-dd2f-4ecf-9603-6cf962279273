<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysUserMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysUser">
    <!--@mbg.generated-->
    <!--@Table `sys_user`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="userName" jdbcType="VARCHAR" property="userName" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="userPhone" jdbcType="VARCHAR" property="userPhone" />
    <result column="userEmail" jdbcType="VARCHAR" property="userEmail" />
    <result column="userGender" jdbcType="VARCHAR" property="userGender" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="createBy" jdbcType="VARCHAR" property="createBy" />
    <result column="updateBy" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap id="UserPageResultMap" type="com.ybda.model.entity.SysUser" extends="BaseResultMap">
    <collection property="userRoles" ofType="com.ybda.model.entity.SysRole">
      <id column="roleId" property="id"/>
      <result column="roleName" property="roleName"/>
      <result column="roleCode" property="roleCode"/>
      <result column="roleDesc" property="roleDesc"/>
      <result column="roleStatus" property="status"/>
    </collection>
    <collection property="userOrgUnits" ofType="com.ybda.model.entity.SysOrgUnits">
      <id column="orgId" property="id"/>
      <result column="orgName" property="name"/>
      <result column="orgCode" property="code"/>
      <result column="orgDesc" property="abbr"/>
      <result column="orgStatus" property="status"/>
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, userName, real_name, `password`, userPhone, userEmail, userGender, `status`, 
    createBy, updateBy, create_time, update_time
  </sql>

  <select id="selectPermissionList" resultType="java.lang.String">
    SELECT pm.resource
    FROM `sys_user`
    INNER JOIN sys_user_role ru ON ru.user_id = sys_user.id
    INNER JOIN sys_role_permission rp ON ru.role_id = rp.role_id
    INNER JOIN sys_permission pm ON rp.permission_id = pm.id
    WHERE sys_user.id = #{id}
    </select>

  <select id="selectroleList" resultType="java.lang.String">
    SELECT role_code
    FROM `sys_user`
    LEFT JOIN sys_user_role ru ON ru.user_id = sys_user.id
    LEFT JOIN sys_role ON ru.role_id = sys_role.id
    WHERE sys_user.id = #{id}
    GROUP BY role_code
  </select>

  <select id="selectUserPageCount" resultType="java.lang.Long">
    SELECT COUNT(DISTINCT u.id)
    FROM sys_user u
           LEFT JOIN sys_user_role ur ON u.id = ur.user_id
           LEFT JOIN sys_role r ON ur.role_id = r.id
    <where>
      <if test="userName != null and userName != ''">
        AND u.userName LIKE CONCAT('%', #{userName}, '%')
      </if>
      <if test="realName != null and realName != ''">
        AND u.real_name LIKE CONCAT('%', #{realName}, '%')
      </if>
      <if test="userPhone != null and userPhone != ''">
        AND u.userPhone LIKE CONCAT('%', #{userPhone}, '%')
      </if>
      <if test="userEmail != null and userEmail != ''">
        AND u.userEmail LIKE CONCAT('%', #{userEmail}, '%')
      </if>
      <if test="userGender != null">
        AND u.userGender = #{userGender}
      </if>
      <if test="status != null">
        AND u.status = #{status}
      </if>
    </where>
  </select>

  <select id="listUsersWithoutOrg" resultMap="BaseResultMap">
    SELECT su.*
    FROM sys_user su
    LEFT JOIN sys_user_org suo ON su.id = suo.user_id
    WHERE suo.org_id IS NULL
    <!-- 可选条件 -->
    <if test="userName != null and userName != ''">
      AND su.userName LIKE CONCAT('%',#{userName}, '%')
    </if>
    <if test="realName != null and realName != ''">
      AND su.real_name LIKE CONCAT('%',#{realName}, '%')
    </if>
    <if test="userEmail != null and userEmail != ''">
      AND su.userEmail LIKE CONCAT('%',#{userEmail}, '%')
    </if>


  </select>

  <select id="listUsersWithOrgs" resultMap="BaseResultMap">
    SELECT su.*
    FROM sys_user su
    INNER JOIN sys_user_org suo ON su.id = suo.user_id
    AND suo.org_id IN
    <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
      #{orgId}
    </foreach>
    <!-- 可选条件 -->
    <if test="userName != null and userName != ''">
      AND su.userName LIKE CONCAT('%',#{userName}, '%')
    </if>
    <if test="realName != null and realName != ''">
      AND su.real_name LIKE CONCAT('%',#{realName}, '%')
    </if>
    <if test="email != null and email != ''">
      AND su.userEmail LIKE CONCAT('%',#{email}, '%')
    </if>

  </select>

  <select id="selectUser" resultMap="UserPageResultMap">
    SELECT u.*,
    r.id AS roleId, r.role_name AS roleName, r.role_code AS roleCode, r.role_desc AS roleDesc, r.status AS roleStatus,
    ou.id AS orgId, ou.name AS orgName, ou.code AS orgCode, ou.description AS orgDesc, ou.status AS orgStatus
    FROM sys_user u
    LEFT JOIN sys_user_role ur ON u.id = ur.user_id
    LEFT JOIN sys_role r ON ur.role_id = r.id
    LEFT JOIN sys_user_org uo ON uo.user_id = u.id
    LEFT JOIN sys_org_units ou ON ou.id = uo.org_id
    WHERE u.id = #{id}
  </select>
</mapper>