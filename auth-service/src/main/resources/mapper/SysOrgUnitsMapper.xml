<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysOrgUnitsMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysOrgUnits">
    <!--@mbg.generated-->
    <!--@Table sys_org_units-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="abbr" jdbcType="VARCHAR" property="abbr" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="ancestors" jdbcType="VARCHAR" property="ancestors" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, parent_id, `name`, code, abbr, `level`, ancestors, description, sort, create_user, 
    create_user_id, create_time, update_user, update_user_id, update_time, `status`
  </sql>

  <select id="listAllDescendants" resultMap="BaseResultMap">
    WITH RECURSIVE cte AS (
    SELECT sou.* FROM sys_org_units sou
    WHERE  sou.parent_id IN
    <foreach close=")" collection="parentIds" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
    UNION ALL
    SELECT t.* FROM sys_org_units t
    INNER JOIN cte
    ON t.parent_id = cte.id)
    SELECT * FROM cte
    ORDER BY sort ASC
    </select>
</mapper>