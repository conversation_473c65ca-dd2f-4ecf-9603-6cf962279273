<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.SysRoleMapper">
  <resultMap id="BaseResultMap" type="com.ybda.model.entity.SysRole">
    <!--@mbg.generated-->
    <!--@Table `sys_role`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="role_code" jdbcType="VARCHAR" property="roleCode" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_desc" jdbcType="VARCHAR" property="roleDesc" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, role_code, role_name, role_desc, `status`, create_by, update_by, create_time, 
    update_time
  </sql>
</mapper>