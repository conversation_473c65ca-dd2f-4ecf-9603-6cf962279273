<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!-- 关闭Logback状态信息输出 -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    
    <!-- 定义日志文件的存储地址 -->
    <property name="LOG_HOME" value="logs"/>
    <property name="LOG_NAME" value="gateway"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%p] %d{HH:mm:ss.SSS} [%t] %c{2}[%L]%m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 应用日志文件输出 -->
    <appender name="APP_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${LOG_NAME}.log</file>
        <encoder>
            <pattern>[%p] %d{HH:mm:ss.SSS} [%t] %c{2}[%L]%m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/gateway/${LOG_NAME}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 访问日志文件输出 -->
    <appender name="ACCESS_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${LOG_NAME}-access.log</file>
        <encoder>
            <pattern>[%p] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/gateway/${LOG_NAME}-access-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 请求日志文件输出 -->
    <appender name="REQUEST_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${LOG_NAME}-request.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/gateway/${LOG_NAME}-request-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 特定的日志记录器配置 -->
    <!-- Spring Cloud Gateway相关日志 -->
    <logger name="org.springframework.cloud.gateway" level="INFO" additivity="false">
        <appender-ref ref="ACCESS_LOG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- Sa-Token响应式相关日志 -->
    <logger name="cn.dev33.satoken" level="INFO" additivity="false">
        <appender-ref ref="ACCESS_LOG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- 网关请求日志 - SaTokenConfigure -->
    <logger name="com.ybda.config.SaTokenConfigure" level="INFO" additivity="false">
        <appender-ref ref="REQUEST_LOG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- 网关日志过滤器 -->
    <logger name="com.ybda.filter.GatewayLogFilter" level="INFO" additivity="false">
        <appender-ref ref="ACCESS_LOG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>



    <!-- 负载均衡相关日志 -->
    <logger name="org.springframework.cloud.loadbalancer" level="WARN" additivity="false">
        <appender-ref ref="APP_LOG"/>
    </logger>

    <!-- Spring框架日志 - 减少输出 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="io.netty" level="WARN"/>
    <logger name="reactor.netty" level="WARN"/>
    
    <!-- Nacos相关日志 - 完全静默 -->
    <logger name="com.alibaba.nacos" level="ERROR"/>
    <logger name="com.alibaba.nacos.client" level="ERROR"/>
    <logger name="com.alibaba.nacos.client.auth" level="ERROR"/>
    <logger name="com.alibaba.nacos.client.config" level="ERROR"/>
    <logger name="com.alibaba.nacos.client.naming" level="ERROR"/>
    <logger name="com.alibaba.nacos.common" level="ERROR"/>
    <logger name="com.alibaba.nacos.common.ability" level="ERROR"/>
    <logger name="com.alibaba.nacos.plugin" level="ERROR"/>
    <logger name="com.alibaba.nacos.plugin.auth" level="ERROR"/>
    <logger name="com.alibaba.nacos.shaded.io.grpc" level="ERROR"/>
    
    <!-- 网关业务日志 -->
    <logger name="com.ybda" level="INFO" additivity="false">
        <appender-ref ref="APP_LOG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- 根日志记录器 - 只显示警告和错误 -->
    <root level="WARN">
        <appender-ref ref="APP_LOG"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
