package com.ybda.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.MultiValueMap;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
public class SaTokenConfigure {
    // 白名单接口（与AuthGlobalFilter一致）
    private static final List<String> WHITE_LIST = Arrays.asList(
            "/auth/auth/login", "/auth/auth/captcha",
            "/jt808/**",  // JT808服务路径放行，由JT808服务内部进行权限控制
            "/auth/internal/**"
    );

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    @Bean
    public SaReactorFilter getSaReactorFilter() {
        return new SaReactorFilter()
                // 拦截全部
                .addInclude("/**")
                // 放行白名单
                .addExclude(WHITE_LIST.toArray(new String[0]))
                // 鉴权方法
                .setAuth(obj -> {
                    String path = cn.dev33.satoken.context.SaHolder.getRequest().getRequestPath();
                    String method = cn.dev33.satoken.context.SaHolder.getRequest().getMethod();
                    log.info("Sa-Token: 鉴权检查 - {} {}", method, path);

                    // 登录校验 - 排除白名单路径
                    SaRouter.match("/**").notMatch(WHITE_LIST.toArray(new String[0])).check(r -> {
                        log.info("开始执行登录校验");
                        StpUtil.checkLogin();
                        String userId = StpUtil.getLoginId().toString();
                        log.info("登录校验通过，用户ID: {}", userId);

                        // 将用户ID存储到请求属性中，供日志过滤器使用
                        cn.dev33.satoken.context.SaHolder.getStorage().set("userId", userId);
                    });

                    log.info("=== 登录校验完成 ===");

                    // 权限控制由各个服务独立处理
                    // Gateway只负责登录校验，权限校验交给后端服务
                })
                // 异常处理：官方推荐状态码细分
                .setError(e -> {
                    String path = cn.dev33.satoken.context.SaHolder.getRequest().getRequestPath();
                    String method = cn.dev33.satoken.context.SaHolder.getRequest().getMethod();
                    String timestamp = LocalDateTime.now().format(FORMATTER);

                    log.error("=== 网关异常 ===");
                    log.error("时间: {}", timestamp);
                    log.error("请求: {} {} | IP: {}", method, path, getClientIp());

                    String errorMessage;
                    int errorCode;

                    if (e instanceof NotLoginException) {
                        NotLoginException ex = (NotLoginException) e;
                        String type = ex.getType();
                        log.error("异常类型: 未登录异常 - {}", type);
                        switch (type) {
                            case NotLoginException.NOT_TOKEN:
                                errorMessage = "未提供Token";
                                errorCode = 40101;
                                break;
                            case NotLoginException.INVALID_TOKEN:
                                errorMessage = "无效Token";
                                errorCode = 40102;
                                break;
                            case NotLoginException.TOKEN_TIMEOUT:
                                errorMessage = "Token已过期";
                                errorCode = 40103;
                                break;
                            case NotLoginException.BE_REPLACED:
                                errorMessage = "Token已被顶下线";
                                errorCode = 40104;
                                break;
                            case NotLoginException.KICK_OUT:
                                errorMessage = "Token已被踢下线";
                                errorCode = 40105;
                                break;
                            default:
                                errorMessage = "当前会话未登录";
                                errorCode = 401;
                                break;
                        }
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorMessage", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorCode", errorCode);
                        return SaResult.error(errorMessage).setCode(errorCode);
                    } else if (e instanceof NotPermissionException) {
                        NotPermissionException ex = (NotPermissionException) e;
                        errorMessage = "无权限: " + ex.getCode();
                        errorCode = 40301;
                        log.error("异常类型: 权限异常 - {}", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorMessage", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorCode", errorCode);
                        return SaResult.error(errorMessage).setCode(errorCode);
                    } else if (e instanceof NotRoleException) {
                        NotRoleException ex = (NotRoleException) e;
                        errorMessage = "无角色: " + ex.getRole();
                        errorCode = 40302;
                        log.error("异常类型: 角色异常 - {}", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorMessage", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorCode", errorCode);
                        return SaResult.error(errorMessage).setCode(errorCode);
                    } else if (e.getClass().getSimpleName().equals("DisableLoginException")) {
                        errorMessage = "账号被禁用";
                        errorCode = 40106;
                        log.error("异常类型: 账号禁用异常 - {}", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorMessage", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorCode", errorCode);
                        return SaResult.error(errorMessage).setCode(errorCode);
                    } else {
                        errorMessage = "鉴权异常: " + e.getMessage();
                        errorCode = 500;
                        log.error("异常类型: 未知异常 - {}", errorMessage, e);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorMessage", errorMessage);
                        cn.dev33.satoken.context.SaHolder.getStorage().set("errorCode", errorCode);
                        return SaResult.error(errorMessage).setCode(errorCode);
                    }
                })
                // 全局CORS处理
                .setBeforeAuth(obj -> {
                    // 设置CORS响应头
                    cn.dev33.satoken.context.SaHolder.getResponse()
                            .setHeader("Access-Control-Allow-Origin", "*")
                            .setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE")
                            .setHeader("Access-Control-Max-Age", "3600")
                            .setHeader("Access-Control-Allow-Headers", "*");

                    // 预检请求直接返回
                    SaRouter.match(cn.dev33.satoken.router.SaHttpMethod.OPTIONS)
                            .free(r -> {
                                String path = cn.dev33.satoken.context.SaHolder.getRequest().getRequestPath();
                                log.info("OPTIONS预检请求放行 - {}", path);
                            })
                            .back();
                });
    }

    /**
     * 获取客户端真实IP
     */
    private String getClientIp() {
        try {
            String xForwardedFor = cn.dev33.satoken.context.SaHolder.getRequest().getHeader("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
                return xForwardedFor.split(",")[0].trim();
            }
            return "127.0.0.1";
        } catch (Exception e) {
            return "unknown";
        }
    }



}