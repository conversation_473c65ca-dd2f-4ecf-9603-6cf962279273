package com.ybda.filter;

import com.ybda.entity.GatewayLog;
import com.ybda.service.GatewayLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.reactivestreams.Publisher;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.core.scheduler.Scheduler;
import cn.dev33.satoken.stp.StpUtil;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ConcurrentLinkedQueue;
import jakarta.annotation.PreDestroy;

import static com.ybda.util.IpUtil.getIpAddr;
import com.ybda.util.CompressionUtil;
import cn.dev33.satoken.stp.StpUtil;

/**
 * 网关日志过滤器 - 请求体读取和日志记录
 * 读取请求体后重新构建请求流，确保转发正确，同时记录详细日志
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GatewayLogFilter implements GlobalFilter, Ordered {

    private final GatewayLogService gatewayLogService;

    // 自定义请求头，转发之前删除自定义请求头
    private static final List<String> CUSTOM_HEADERS = Arrays.asList("sign", "timestamp", "random", "Request-Origin-App");

    // 时间格式化器
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    // 专用线程池 - 针对网关日志异步处理优化
    private static final Scheduler LOG_SCHEDULER = Schedulers.newBoundedElastic(
            4,              // 核心线程数：适中，避免过多线程竞争
            50,             // 最大线程数：控制资源使用
            "gateway-log",  // 线程名前缀：便于监控和调试
            60,             // 空闲超时：60秒后回收线程
            true            // 守护线程：应用关闭时自动结束
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String method = request.getMethod().name();
        String path = request.getPath().pathWithinApplication().value();
        long startTime = System.currentTimeMillis();
        // 创建日志对象用于记录完整请求信息
        GatewayLog gatewayLog = GatewayLog.builder()
                .requestMethod(method)
                .requestPath(path)
                .requestTime(LocalDateTime.now().format(FORMATTER))
                .startTime(startTime)
                .ip(getIpAddr(request))
//                .userAgent(request.getHeaders().getFirst("User-Agent"))
                .referer(request.getHeaders().getFirst("Referer"))
                // ✅ 补充容易获取的字段
                .schema(request.getURI().getScheme())
                .targetServer(getTargetServer(exchange))
                .userId(getCurrentUserId(exchange))
//                .isWhiteListed(isWhiteListedPath(path))
                .build();

        // 基本日志记录
        log.info("🔵 网关请求开始: {} {} | IP: {}", method, path, gatewayLog.getIp());

        // 记录查询参数
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        if (!queryParams.isEmpty()) {
            log.info("📋 查询参数: {}", queryParams);
        }

        // 检查是否需要读取并重建请求体
        MediaType contentType = request.getHeaders().getContentType();
        long contentLength = request.getHeaders().getContentLength();
        
        if (contentType != null && contentLength > 0 && 
            (MediaType.APPLICATION_JSON.isCompatibleWith(contentType) || 
             MediaType.APPLICATION_FORM_URLENCODED.isCompatibleWith(contentType))) {
            
            log.info("📦 流式读取请求体: 类型={}, 大小={}字节", contentType, contentLength);
            
            // 创建流式处理的请求装饰器
            ServerHttpRequest streamingRequest = createStreamingRequest(request, gatewayLog);
            
            log.info("🚀 流式处理就绪，开始转发: {} {}", method, path);
            
            // 使用流式请求进行转发（最低延迟）
            return continueFilterChain(exchange.mutate().request(streamingRequest).build(), chain, gatewayLog);
            
        } else {
            // 无需读取请求体的情况
            if (contentType != null || contentLength > 0) {
                log.info("📦 请求体信息: 类型={}, 大小={}字节 (不读取内容)", contentType, contentLength);
//                gatewayLog.setRequestSize(contentLength);
            } else {
                log.info("📄 无请求体");
//                gatewayLog.setRequestSize(0L);
            }

            return continueFilterChain(exchange, chain, gatewayLog);
        }
    }

    /**
     * 创建流式处理的请求装饰器
     */
    private ServerHttpRequest createStreamingRequest(ServerHttpRequest originalRequest, GatewayLog gatewayLog) {
        // 线程安全的数据收集器
        final ConcurrentLinkedQueue<String> chunkQueue = new ConcurrentLinkedQueue<>();
        final AtomicLong totalSize = new AtomicLong(0);
        
        return new ServerHttpRequestDecorator(originalRequest) {
            @Override
            public Flux<DataBuffer> getBody() {
                return super.getBody()
                    .doOnNext(dataBuffer -> {
                        // 🚀 关键：立即复制数据，然后异步处理
                        int readableBytes = dataBuffer.readableByteCount();
                        if (readableBytes > 0) {
                            try {
                                // 快速复制数据（主线程中最小化操作）
                                byte[] bytes = new byte[readableBytes];
                                dataBuffer.slice(dataBuffer.readPosition(), readableBytes).read(bytes);
                                
                                // ✅ 真正异步：将处理放到独立线程池
                                Mono.fromRunnable(() -> {
                                    try {
                                        String chunk = new String(bytes, StandardCharsets.UTF_8);
                                        chunkQueue.offer(chunk);  // 线程安全添加
                                        totalSize.addAndGet(bytes.length);
                                        
                                        // 异步日志（不阻塞主线程）
                                        log.debug("📄 异步处理数据块: {}字节", bytes.length);
                                    } catch (Exception e) {
                                        log.warn("⚠️ 异步处理数据块异常: {}", e.getMessage());
                                    }
                                                                 })
                                 .subscribeOn(LOG_SCHEDULER)  // 专用日志线程池
                                 .subscribe();  // 不等待结果，立即返回
                                
                            } catch (Exception e) {
                                // 主线程异常处理（最小化）
                                log.warn("⚠️ 数据复制异常: {}", e.getMessage());
                            }
                        }
                    })
                    .doOnComplete(() -> {
                        // ✅ 流结束时也异步处理
                        Mono.fromRunnable(() -> {
                            try {
                                // 异步聚合所有数据块
                                StringBuilder fullBody = new StringBuilder();
                                for (String chunk : chunkQueue) {
                                    fullBody.append(chunk);
                                }
                                
                                String requestBody = fullBody.toString();
                                
                                // 🗜️ 智能压缩存储请求体
                                String compressedRequestBody = CompressionUtil.smartCompress(requestBody);
                                gatewayLog.setRequestBody(compressedRequestBody);
//                                gatewayLog.setRequestSize(totalSize.get());
                                
                                // 记录压缩效果
                                if (CompressionUtil.isCompressed(compressedRequestBody)) {
                                    double ratio = CompressionUtil.getCompressionRatio(requestBody, compressedRequestBody);
                                    log.info("📄 请求体压缩存储: {}字节 → {}字节 (压缩{:.1f}%) | 内容: {}", 
                                        requestBody.length(), compressedRequestBody.length(), ratio,
                                        requestBody.length() > 200 ? requestBody.substring(0, 200) + "..." : requestBody);
                                } else {
                                    log.info("📄 请求体完整内容: {}", requestBody.length() > 200 ?
                                            requestBody.substring(0, 200) + "..." : requestBody);
                                }
                            } catch (Exception e) {
                                log.warn("⚠️ 异步聚合数据异常: {}", e.getMessage());
                            }
                        })
                        .subscribeOn(LOG_SCHEDULER)
                        .subscribe();
                    })
                    .doOnError(error -> {
                        // 异步错误处理
                        Mono.fromRunnable(() -> {
                            log.error("❌ 流式读取请求体异常: {}", error.getMessage());
//                            gatewayLog.setErrorMessage("流式读取异常: " + error.getMessage());
                        })
                        .subscribeOn(LOG_SCHEDULER)
                        .subscribe();
                    });
            }
            
            @Override
            public HttpHeaders getHeaders() {
                // 清理自定义请求头
                HttpHeaders headers = new HttpHeaders();
                headers.putAll(super.getHeaders());
                
                // 删除自定义header
                for (String customHeader : CUSTOM_HEADERS) {
                    headers.remove(customHeader);
                }
                
                return headers;
            }
        };
    }

    /**
     * 继续过滤链处理 - 包含响应体拦截
     */
    private Mono<Void> continueFilterChain(ServerWebExchange exchange, GatewayFilterChain chain, GatewayLog gatewayLog) {
        // 对于没有请求体的请求，仍需要清理请求头
        ServerHttpRequest request = exchange.getRequest();
        if (!(request instanceof ServerHttpRequestDecorator)) {
            // 如果不是装饰器（即没有重建），需要清理请求头
            ServerHttpRequest mutableReq = request.mutate().headers(httpHeaders -> {
                for (String customHeader : CUSTOM_HEADERS) {
                    httpHeaders.remove(customHeader);
                }
            }).build();
            exchange = exchange.mutate().request(mutableReq).build();
        }

        // 🆕 创建响应体拦截装饰器
        ServerHttpResponse response = exchange.getResponse();
        ServerHttpResponseDecorator decoratedResponse = createResponseInterceptor(response, gatewayLog);
        
        // 使用包装后的响应对象
        ServerWebExchange decoratedExchange = exchange.mutate().response(decoratedResponse).build();

        // 执行过滤链
        return chain.filter(decoratedExchange)
                .doOnSuccess(unused -> {
                    // 记录成功完成
                    finishLogging(gatewayLog, response.getStatusCode().value(), "SUCCESS", null);
                })
                .doOnError(error -> {
                    // 记录异常完成
                    finishLogging(gatewayLog, 500, "ERROR", error.getMessage());
                });
    }
    
    /**
     * 创建响应体拦截装饰器 - 流式处理版本（修复DataBuffer引用计数问题）
     */
    private ServerHttpResponseDecorator createResponseInterceptor(ServerHttpResponse originalResponse, GatewayLog gatewayLog) {
        // 线程安全的响应体收集器
        final ConcurrentLinkedQueue<String> responseChunks = new ConcurrentLinkedQueue<>();
        final AtomicLong responseSize = new AtomicLong(0);
        
        return new ServerHttpResponseDecorator(originalResponse) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                return super.writeWith(
                    Flux.from(body)
                        .doOnNext(dataBuffer -> {
                            // 🚀 流式处理：逐块读取，不阻塞响应
                            try {
                                int readableBytes = dataBuffer.readableByteCount();
                                if (readableBytes > 0) {
                                    // 快速复制数据（主线程最小化操作）
                                    byte[] bytes = new byte[readableBytes];
                                    dataBuffer.slice(dataBuffer.readPosition(), readableBytes).read(bytes);
                                    
                                    // ✅ 异步处理响应体记录
                                    Mono.fromRunnable(() -> {
                                        try {
                                            String chunk = new String(bytes, StandardCharsets.UTF_8);
                                            responseChunks.offer(chunk);
                                            responseSize.addAndGet(bytes.length);
                                            
                                            log.debug("📤 响应数据块: {}字节", bytes.length);
                                        } catch (Exception e) {
                                            log.warn("⚠️ 响应体块处理异常: {}", e.getMessage());
                                        }
                                    })
                                    .subscribeOn(LOG_SCHEDULER)
                                    .subscribe();
                                }
                            } catch (Exception e) {
                                log.warn("⚠️ 响应体复制异常: {}", e.getMessage());
                            }
                        })
                        .doOnComplete(() -> {
                            // ✅ 流结束时异步聚合响应体
                            Mono.fromRunnable(() -> {
                                try {
                                    StringBuilder fullResponse = new StringBuilder();
                                    for (String chunk : responseChunks) {
                                        fullResponse.append(chunk);
                                    }
                                    
                                    String responseBody = fullResponse.toString();
                                    
                                    // 🗜️ 智能压缩存储响应体
                                    String compressedResponseBody = CompressionUtil.smartCompress(responseBody);
                                    gatewayLog.setResponseData(compressedResponseBody);
//                                    gatewayLog.setResponseSize(responseSize.get());
                                    
                                    // 记录压缩效果
                                    if (CompressionUtil.isCompressed(compressedResponseBody)) {
                                        double ratio = CompressionUtil.getCompressionRatio(responseBody, compressedResponseBody);
                                        log.info("📤 响应体压缩存储: {}字节 → {}字节 (压缩{:.1f}%)", 
                                            responseBody.length(), compressedResponseBody.length(), ratio);
                                    } else {
                                        log.debug("📤 响应体完整记录: {}字节", responseSize.get());
                                    }
                                } catch (Exception e) {
                                    log.warn("⚠️ 响应体聚合异常: {}", e.getMessage());
                                }
                            })
                            .subscribeOn(LOG_SCHEDULER)
                            .subscribe();
                        })
                        .doOnError(error -> {
                            log.warn("⚠️ 响应体流处理异常: {}", error.getMessage());
                        })
                );
            }
        };
    }
    
    /**
     * 完成日志记录
     */
    private void finishLogging(GatewayLog gatewayLog, int statusCode, String status, String errorMessage) {
        // ✅ 异步完成日志记录，不阻塞响应
        Mono.fromRunnable(() -> {
            long endTime = System.currentTimeMillis();
            gatewayLog.setEndTime(endTime);
            gatewayLog.setExecuteTime(endTime - gatewayLog.getStartTime());
            gatewayLog.setResponseTime(LocalDateTime.now().format(FORMATTER));
            gatewayLog.setStatusCode(statusCode);
            gatewayLog.setStatus(status);
            
//            if (errorMessage != null) {
//                gatewayLog.setErrorMessage(errorMessage);
//                log.error("❌ 网关请求异常: {} {} | 耗时: {}ms - {}",
//                        gatewayLog.getRequestMethod(), gatewayLog.getRequestPath(),
//                        gatewayLog.getExecuteTime(), errorMessage);
//            } else {
//                log.info("✅ 网关请求完成: {} {} | 耗时: {}ms",
//                        gatewayLog.getRequestMethod(), gatewayLog.getRequestPath(),
//                        gatewayLog.getExecuteTime());
//            }

            // 异步保存到数据库
            gatewayLogService.saveGatewayLogAsync(gatewayLog);
        })
        .subscribeOn(LOG_SCHEDULER)
        .subscribe();
    }


    @Override
    public int getOrder() {
        return -50; // 确保在SaToken过滤器之后执行，以便能获取到用户ID
    }
    
    /**
     * 获取目标服务器信息
     */
    private String getTargetServer(ServerWebExchange exchange) {
        try {
            // 优先从Route对象中提取服务名
            Object routeObj = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
            if (routeObj != null) {
                String routeStr = routeObj.toString();
                // 从Route字符串中提取服务名，格式：Route{id='auth-service', uri=lb://auth-service, ...}
                if (routeStr.contains("id='")) {
                    int startIndex = routeStr.indexOf("id='") + 4;
                    int endIndex = routeStr.indexOf("'", startIndex);
                    if (endIndex > startIndex) {
                        return routeStr.substring(startIndex, endIndex);
                    }
                }
            }

            // 备选方案：从请求URL中提取服务名
            Object requestUrlObj = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR);
            if (requestUrlObj != null) {
                String requestUrl = requestUrlObj.toString();
                // 从URL中提取服务名，格式：lb://auth-service/...
                if (requestUrl.startsWith("lb://")) {
                    String servicePart = requestUrl.substring(5); // 去掉 "lb://"
                    int slashIndex = servicePart.indexOf('/');
                    if (slashIndex > 0) {
                        return servicePart.substring(0, slashIndex);
                    } else {
                        return servicePart;
                    }
                }
                return requestUrl;
            }
        } catch (Exception e) {
            log.debug("获取目标服务器信息异常: {}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取当前登录用户ID（Reactive安全版本）
     */
    private String getCurrentUserId(ServerWebExchange exchange) {
        try {
            // 方案3: 尝试从Token头中手动解析
            String token = getTokenFromRequest(exchange.getRequest());
            if (token != null) {
                Object parsedUserId = parseUserIdFromToken(token);
                if (parsedUserId != null) {
                    log.debug("从Token解析到用户ID: {}", parsedUserId);
                    return parsedUserId.toString();
                }
            }
        } catch (Exception e) {
            // 任何异常都不影响正常转发
            log.debug("获取用户ID异常，可能未登录: {}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 从请求中提取Token
     */
    private String getTokenFromRequest(ServerHttpRequest request) {
        // 从Authorization头获取
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        // 从satoken头获取（SaToken默认）
        String saTokenHeader = request.getHeaders().getFirst("satoken");
        if (saTokenHeader != null) {
            return saTokenHeader;
        }
        
        // 从cookie获取
        if (request.getCookies().containsKey("satoken")) {
            return request.getCookies().getFirst("satoken").getValue();
        }
        
        return null;
    }
    
    /**
     * 从Token中解析用户ID（安全方式）
     */
    private Object parseUserIdFromToken(String token) {
        try {
            // 使用SaToken的工具方法解析（不依赖上下文）
            return StpUtil.stpLogic.getLoginIdByToken(token);
        } catch (Exception e) {
            log.debug("Token解析失败: {}", e.getMessage());
            return null;
        }
    }
}
