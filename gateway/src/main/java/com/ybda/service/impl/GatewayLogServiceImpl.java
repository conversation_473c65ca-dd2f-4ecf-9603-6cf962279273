package com.ybda.service.impl;

import com.ybda.entity.GatewayLog;
import com.ybda.service.GatewayLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 网关日志服务实现类
 * 处理网关日志的MongoDB存储逻辑
 */
@Slf4j
@Service
public class GatewayLogServiceImpl implements GatewayLogService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void saveGatewayLog(GatewayLog gatewayLog) {
        try {
            // 直接保存到MongoDB
            mongoTemplate.save(gatewayLog);
        } catch (Exception e) {
            log.error("保存网关日志失败: {} {} - {}",
                    gatewayLog.getRequestMethod(),
                    gatewayLog.getRequestPath(),
                    e.getMessage(), e);
            throw new RuntimeException("MongoDB存储失败", e);
        }
    }

    @Async
    @Override
    public void saveGatewayLogAsync(GatewayLog gatewayLog) {
        saveGatewayLog(gatewayLog);
    }
}
