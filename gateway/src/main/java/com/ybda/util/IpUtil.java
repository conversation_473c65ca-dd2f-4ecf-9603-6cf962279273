package com.ybda.util;

import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;

/**
 * IP工具类
 * 用于获取客户端真实IP地址
 */
public class IpUtil {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IP地址
     * 
     * @param request ServerHttpRequest对象
     * @return 客户端IP地址
     */
    public static String getIpAddr(ServerHttpRequest request) {
        String ip = null;
        
        try {
            // 1. X-Forwarded-For：Squid 服务代理
            ip = request.getHeaders().getFirst("X-Forwarded-For");
            if (isValidIp(ip)) {
                // 多次反向代理后会有多个ip值，第一个ip才是真实ip
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }

            // 2. Proxy-Client-IP：apache 服务代理
            ip = request.getHeaders().getFirst("Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 3. WL-Proxy-Client-IP：weblogic 服务代理
            ip = request.getHeaders().getFirst("WL-Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 4. HTTP_CLIENT_IP：有些代理服务器
            ip = request.getHeaders().getFirst("HTTP_CLIENT_IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 5. HTTP_X_FORWARDED_FOR：有些代理服务器
            ip = request.getHeaders().getFirst("HTTP_X_FORWARDED_FOR");
            if (isValidIp(ip)) {
                return ip;
            }

            // 6. X-Real-IP：nginx服务代理
            ip = request.getHeaders().getFirst("X-Real-IP");
            if (isValidIp(ip)) {
                return ip;
            }

            // 7. 如果以上都没有获取到，则使用远程地址
            if (request.getRemoteAddress() != null) {
                ip = request.getRemoteAddress().getAddress().getHostAddress();
                // IPv6本地地址转换为IPv4
                if (LOCALHOST_IPV6.equals(ip)) {
                    ip = LOCALHOST_IPV4;
                }
                return ip;
            }

        } catch (Exception e) {
            // 异常情况返回默认IP
            return LOCALHOST_IPV4;
        }

        return LOCALHOST_IPV4;
    }

    /**
     * 检查IP是否有效
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && 
               !UNKNOWN.equalsIgnoreCase(ip) && 
               !ip.trim().isEmpty();
    }
}
