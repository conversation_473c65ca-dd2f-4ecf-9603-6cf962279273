HELP.md
target/
.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
.cunzhi-memory

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### 日志文件 ###
# 忽略所有日志文件和日志目录
logs/
*.log
*.log.*
*.out

# 忽略各模块的日志目录
auth-service/logs/
gateway/logs/
JT808/jtt808-server/logs/
common/logs/

# 忽略Nacos日志
nacos-logs/

# 忽略Spring Boot默认日志
spring.log
application.log

# 忽略其他常见日志格式
*.log.gz
*.log.zip
*.log.tar.gz
